const SteamUser = require('steam-user');
const SteamID = require('steamid');
const protobuf = require('protobufjs');
const fs = require('fs');
const path = require("path");

// 尝试加载配置文件
let config;
try {
    config = require('./config.js');
} catch (err) {
    console.error('❌ 配置文件 config.js 不存在，请复制 config.example.js 为 config.js 并填入你的信息');
    process.exit(1);
}

// Steam 客户端
const client = new SteamUser();

// CSGO AppID
const CSGO_APP_ID = config.app.csgoAppId;

// 消息类型常量
const MSG_TYPES = {
    OPEN_CRATE: 2534,
    UNLOCK_CRATE: 1007,
    UNLOCK_CRATE_RESPONSE: 1008,
    ITEM_CUSTOMIZATION: 1090,
    USE_ITEM_REQUEST: 1025,
    USE_ITEM_RESPONSE: 1026
};

// proto 文件
const root = protobuf.loadSync(
    path.join(__dirname, "proto", "econ_gcmessages.proto")
);

// 加载开箱消息结构
const openCrateRoot = protobuf.loadSync(
    path.join(__dirname, "proto", "opencrate.proto")
);

const ItemCustomization = root.lookupType('CMsgGCItemCustomizationNotification');
const OpenCrateMsg = openCrateRoot.lookupType('CMsgOpenCrate');

// 开箱状态跟踪
let openingCrates = new Map();
let isConnectedToGC = false;

// 日志系统
class Logger {
    constructor(config) {
        this.level = config.logging.level || 'INFO';
        this.saveToFile = config.logging.saveToFile || false;
        this.logFile = config.logging.logFile || 'opencase.log';
        this.levels = { DEBUG: 0, INFO: 1, WARNING: 2, ERROR: 3 };
    }
    
    log(message, type = 'INFO') {
        if (this.levels[type] < this.levels[this.level]) return;
        
        const timestamp = new Date().toISOString();
        const prefix = {
            'DEBUG': '🔍',
            'INFO': '📝',
            'SUCCESS': '✅',
            'ERROR': '❌',
            'WARNING': '⚠️',
            'CRATE': '📦'
        }[type] || '📝';
        
        const logMessage = `[${timestamp}] ${prefix} ${message}`;
        console.log(logMessage);
        
        if (this.saveToFile) {
            fs.appendFileSync(this.logFile, logMessage + '\n');
        }
    }
}

const logger = new Logger(config);

// 工具函数
function delay(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
}

// Steam 事件处理
client.on('loggedOn', () => {
    logger.log('已登录 Steam', 'SUCCESS');
    client.gamesPlayed([CSGO_APP_ID]);
});

client.on('playingState', (blocked, apps) => {
    logger.log(`正在玩游戏状态: ${JSON.stringify(apps)}`, 'SUCCESS');
});

client.on('appLaunched', (appid) => {
    if (appid === CSGO_APP_ID) {
        logger.log('CSGO 应用已启动', 'SUCCESS');
    }
});

client.on('gcConnected', (appid) => {
    if (appid === CSGO_APP_ID) {
        isConnectedToGC = true;
        logger.log('已连接到 CSGO 游戏协调器', 'SUCCESS');
    }
});

client.on('gcDisconnected', (appid) => {
    if (appid === CSGO_APP_ID) {
        isConnectedToGC = false;
        logger.log('与 CSGO 游戏协调器断开连接', 'WARNING');
    }
});

client.on('error', (err) => {
    logger.log(`Steam 客户端错误: ${err.message}`, 'ERROR');
});

// GC 消息处理
client.on('gcMessage', (appid, msgType, proto) => {
    if (appid !== CSGO_APP_ID) return;

    logger.log(`收到 GC 消息，类型: ${msgType}`, 'DEBUG');
    
    switch (msgType) {
        case MSG_TYPES.ITEM_CUSTOMIZATION:
            handleItemCustomization(proto);
            break;
        case MSG_TYPES.UNLOCK_CRATE_RESPONSE:
            handleUnlockCrateResponse(proto);
            break;
        case MSG_TYPES.USE_ITEM_RESPONSE:
            handleUseItemResponse(proto);
            break;
        default:
            if (msgType >= 1000 && msgType <= 3000) {
                logger.log(`未处理的 GC 消息类型: ${msgType}`, 'DEBUG');
            }
            break;
    }
});

// 处理物品掉落通知
function handleItemCustomization(proto) {
    try {
        const decoded = ItemCustomization.decode(proto);
        
        logger.log('收到物品掉落通知', 'CRATE');
        
        if (decoded.item_id && decoded.item_id.length > 0) {
            decoded.item_id.forEach((id, index) => {
                logger.log(`掉落物品 ${index + 1} 资产ID: ${id}`, 'SUCCESS');
                
                // 检查是否是我们正在跟踪的开箱
                for (let [crateKey, crateInfo] of openingCrates) {
                    if (Date.now() - crateInfo.timestamp < 60000) {
                        crateInfo.droppedItems.push(id);
                        logger.log(`开箱 ${crateKey} 获得物品: ${id}`, 'CRATE');
                    }
                }
            });
        }

        if (decoded.extra_data && decoded.extra_data.length > 0) {
            decoded.extra_data.forEach((data, index) => {
                logger.log(`额外数据 ${index + 1}: ${data}`, 'DEBUG');
            });
        }
        
    } catch (err) {
        logger.log(`解析物品掉落消息失败: ${err.message}`, 'ERROR');
    }
}

function handleUnlockCrateResponse(proto) {
    try {
        logger.log('收到开箱响应', 'CRATE');
    } catch (err) {
        logger.log(`解析开箱响应失败: ${err.message}`, 'ERROR');
    }
}

function handleUseItemResponse(proto) {
    try {
        logger.log('收到使用物品响应', 'DEBUG');
    } catch (err) {
        logger.log(`解析使用物品响应失败: ${err.message}`, 'ERROR');
    }
}

// 开箱核心类
class CrateOpener {
    constructor(client, logger, config) {
        this.client = client;
        this.logger = logger;
        this.config = config;
        this.openingCrates = new Map();
    }
    
    async unlockCrate(keyId, caseId, options = {}) {
        const crateKey = `${keyId}_${caseId}`;
        
        if (!isConnectedToGC) {
            this.logger.log('未连接到游戏协调器，等待连接...', 'WARNING');
            
            let waitTime = 0;
            while (!isConnectedToGC && waitTime < this.config.app.gcConnectionTimeout) {
                await delay(1000);
                waitTime += 1000;
            }
            
            if (!isConnectedToGC) {
                this.logger.log('连接游戏协调器超时', 'ERROR');
                return { success: false, error: '连接游戏协调器超时' };
            }
        }

        try {
            const crateInfo = {
                keyId,
                caseId,
                timestamp: Date.now(),
                droppedItems: [],
                retries: 0
            };
            
            this.openingCrates.set(crateKey, crateInfo);
            openingCrates.set(crateKey, crateInfo); // 全局引用
            
            this.logger.log(`开始开箱 - 钥匙ID: ${keyId}, 箱子ID: ${caseId}`, 'CRATE');
            
            const payload = {
                tool_item_id: keyId,
                subject_item_id: caseId,
                for_rental: options.forRental || false
            };
            
            const encodedPayload = OpenCrateMsg.encode(payload).finish();
            this.client.sendToGC(CSGO_APP_ID, MSG_TYPES.OPEN_CRATE, null, encodedPayload);
            
            this.logger.log(`开箱请求已发送`, 'SUCCESS');
            
            const result = await this.waitForCrateResult(crateKey, options.timeout || this.config.crate.timeout);
            return result;
            
        } catch (err) {
            this.logger.log(`开箱失败: ${err.message}`, 'ERROR');
            this.openingCrates.delete(crateKey);
            openingCrates.delete(crateKey);
            return { success: false, error: err.message };
        }
    }
    
    waitForCrateResult(crateKey, timeout = 30000) {
        return new Promise((resolve) => {
            const startTime = Date.now();
            
            const checkResult = () => {
                const crateInfo = this.openingCrates.get(crateKey);
                
                if (!crateInfo) {
                    resolve({ success: false, error: '开箱记录丢失' });
                    return;
                }
                
                if (crateInfo.droppedItems.length > 0) {
                    this.logger.log(`开箱成功！获得 ${crateInfo.droppedItems.length} 个物品`, 'SUCCESS');
                    this.openingCrates.delete(crateKey);
                    openingCrates.delete(crateKey);
                    resolve({ 
                        success: true, 
                        items: crateInfo.droppedItems,
                        duration: Date.now() - crateInfo.timestamp
                    });
                    return;
                }
                
                if (Date.now() - startTime > timeout) {
                    this.logger.log('开箱超时', 'ERROR');
                    this.openingCrates.delete(crateKey);
                    openingCrates.delete(crateKey);
                    resolve({ success: false, error: '开箱超时' });
                    return;
                }
                
                setTimeout(checkResult, 1000);
            };
            
            checkResult();
        });
    }
    
    async unlockMultipleCrates(crateList, options = {}) {
        const results = [];
        const batchDelay = options.batchDelay || this.config.crate.batchDelay;
        
        this.logger.log(`开始批量开箱，共 ${crateList.length} 个箱子`, 'CRATE');
        
        for (let i = 0; i < crateList.length; i++) {
            const { keyId, caseId } = crateList[i];
            
            this.logger.log(`批量开箱进度: ${i + 1}/${crateList.length}`, 'INFO');
            
            const result = await this.unlockCrate(keyId, caseId, options);
            results.push({ keyId, caseId, ...result });
            
            if (i < crateList.length - 1) {
                await delay(batchDelay);
            }
        }
        
        const successful = results.filter(r => r.success).length;
        const failed = results.length - successful;
        
        this.logger.log(`批量开箱完成 - 成功: ${successful}, 失败: ${failed}`, 'SUCCESS');
        
        return results;
    }
}

// 创建开箱器实例
const crateOpener = new CrateOpener(client, logger, config);

// 登录
client.logOn({
    accountName: config.steam.accountName,
    password: config.steam.password,
    twoFactorCode: require('steam-totp').getAuthCode(config.steam.sharedSecret)
});

// 导出主要功能
module.exports = {
    client,
    logger,
    crateOpener,
    unlockCrate: (keyId, caseId, options) => crateOpener.unlockCrate(keyId, caseId, options),
    unlockMultipleCrates: (crateList, options) => crateOpener.unlockMultipleCrates(crateList, options)
};

// 优雅关闭
process.on('SIGINT', () => {
    logger.log('正在关闭程序...', 'INFO');
    client.logOff();
    process.exit(0);
});
