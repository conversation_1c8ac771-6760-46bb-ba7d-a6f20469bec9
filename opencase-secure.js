const SteamUser = require('steam-user');
const SteamID = require('steamid');
const protobuf = require('protobufjs');
const fs = require('fs');
const path = require("path");

// 尝试加载配置文件
let config;
try {
    config = require('./config.js');
} catch (err) {
    console.error('❌ 配置文件 config.js 不存在，请复制 config.example.js 为 config.js 并填入你的信息');
    process.exit(1);
}

// Steam 客户端
const client = new SteamUser();

// CSGO AppID
const CSGO_APP_ID = config.app.csgoAppId;

// 消息类型常量
const MSG_TYPES = {
    OPEN_CRATE: 2534,
    UNLOCK_CRATE: 1007,
    UNLOCK_CRATE_RESPONSE: 1008,
    ITEM_CUSTOMIZATION: 1090,
    USE_ITEM_REQUEST: 1025,
    USE_ITEM_RESPONSE: 1026
};

// proto 文件
const root = protobuf.loadSync(
    path.join(__dirname, "proto", "econ_gcmessages.proto")
);

// 加载开箱消息结构
const openCrateRoot = protobuf.loadSync(
    path.join(__dirname, "proto", "opencrate.proto")
);

// 加载 GC 消息结构
let gcRoot, ClientHelloType;
try {
    gcRoot = protobuf.loadSync(path.join(__dirname, "proto", "gc_messages.proto"));
    ClientHelloType = gcRoot.lookupType('CMsgClientHello');
} catch (err) {
    console.log('⚠️ GC 消息 proto 加载失败，将使用空消息');
    ClientHelloType = null;
}

const ItemCustomization = root.lookupType('CMsgGCItemCustomizationNotification');
const OpenCrateMsg = openCrateRoot.lookupType('CMsgOpenCrate');

// 开箱状态跟踪
let openingCrates = new Map();
let isConnectedToGC = false;

// 日志系统
class Logger {
    constructor(config) {
        this.level = config.logging.level || 'INFO';
        this.saveToFile = config.logging.saveToFile || false;
        this.logFile = config.logging.logFile || 'opencase.log';
        this.levels = { DEBUG: 0, INFO: 1, WARNING: 2, ERROR: 3 };
    }
    
    log(message, type = 'INFO') {
        if (this.levels[type] < this.levels[this.level]) return;
        
        const timestamp = new Date().toISOString();
        const prefix = {
            'DEBUG': '🔍',
            'INFO': '📝',
            'SUCCESS': '✅',
            'ERROR': '❌',
            'WARNING': '⚠️',
            'CRATE': '📦'
        }[type] || '📝';
        
        const logMessage = `[${timestamp}] ${prefix} ${message}`;
        console.log(logMessage);
        
        if (this.saveToFile) {
            fs.appendFileSync(this.logFile, logMessage + '\n');
        }
    }
}

const logger = new Logger(config);

// 工具函数
function delay(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
}

// 创建正确的 ClientHello 消息
function createClientHelloMessage() {
    if (!ClientHelloType) {
        logger.log('使用空消息作为 ClientHello', 'WARNING');
        return Buffer.alloc(0);
    }

    // 使用更完整的 ClientHello 消息结构
    const helloData = {
        version: 2000202,              // 与 C# 代码一致
        socache_have_versions: [],     // 空的缓存版本列表
        client_session_need: 1,        // 需要客户端会话
        client_launcher: 0,            // 客户端启动器类型
        steam_launcher: 1              // Steam 启动器
    };

    try {
        const encoded = ClientHelloType.encode(helloData).finish();
        logger.log(`ClientHello 消息创建成功 (版本: ${helloData.version}, 大小: ${encoded.length} 字节)`, 'INFO');
        return encoded;
    } catch (err) {
        logger.log(`ClientHello 编码失败: ${err.message}`, 'ERROR');
        return Buffer.alloc(0);
    }
}

// 尝试多种连接方法
function attemptMultipleConnectionMethods() {
    logger.log('尝试多种 GC 连接方法', 'INFO');

    // 方法1: 标准 ClientHello
    setTimeout(() => {
        if (!isConnectedToGC) {
            logger.log('方法1: 发送标准 ClientHello', 'INFO');
            const helloMessage = createClientHelloMessage();
            client.sendToGC(CSGO_APP_ID, 4006, null, helloMessage);
        }
    }, 1000);

    // 方法2: 请求 ClientWelcome
    setTimeout(() => {
        if (!isConnectedToGC) {
            logger.log('方法2: 请求 ClientWelcome', 'INFO');
            client.sendToGC(CSGO_APP_ID, 4004, null, Buffer.alloc(0));
        }
    }, 3000);

    // 方法3: 连接状态消息
    setTimeout(() => {
        if (!isConnectedToGC) {
            logger.log('方法3: 发送连接状态消息', 'INFO');
            client.sendToGC(CSGO_APP_ID, 4001, null, Buffer.alloc(0));
        }
    }, 5000);

    // 方法4: 重新发送 ClientHello
    setTimeout(() => {
        if (!isConnectedToGC) {
            logger.log('方法4: 重新发送 ClientHello', 'INFO');
            const helloMessage = createClientHelloMessage();
            client.sendToGC(CSGO_APP_ID, 4006, null, helloMessage);
        }
    }, 8000);
}

// Steam 事件处理
client.on('loggedOn', () => {
    logger.log('已登录 Steam', 'SUCCESS');
    client.gamesPlayed([CSGO_APP_ID]);
});

client.on('playingState', (blocked, apps) => {
    logger.log(`正在玩游戏状态: ${JSON.stringify(apps)}`, 'SUCCESS');
});

client.on('appLaunched', (appid) => {
    if (appid === CSGO_APP_ID) {
        logger.log('CSGO 应用已启动，尝试连接游戏协调器...', 'SUCCESS');
        // 主动请求连接到 GC - 使用正确的参数格式
        setTimeout(() => {
            if (!isConnectedToGC) {
                logger.log('主动请求连接到游戏协调器', 'INFO');
                const helloMessage = createClientHelloMessage();
                // 关键修正：使用 {} 而不是 null 作为 protobuf header
                client.sendToGC(CSGO_APP_ID, 4006, {}, helloMessage);
            }
        }, 2000);
    }
});

client.on('gcConnected', (appid) => {
    if (appid === CSGO_APP_ID) {
        isConnectedToGC = true;
        logger.log('已连接到 CSGO 游戏协调器', 'SUCCESS');
    }
});

client.on('gcDisconnected', (appid) => {
    if (appid === CSGO_APP_ID) {
        isConnectedToGC = false;
        logger.log('与 CSGO 游戏协调器断开连接', 'WARNING');

        // 尝试重新连接
        setTimeout(() => {
            if (!isConnectedToGC) {
                logger.log('尝试重新连接游戏协调器', 'INFO');
                const helloMessage = createClientHelloMessage();
                // 关键修正：使用 {} 而不是 null
                client.sendToGC(CSGO_APP_ID, 4006, {}, helloMessage);
            }
        }, 5000);
    }
});

client.on('error', (err) => {
    logger.log(`Steam 客户端错误: ${err.message}`, 'ERROR');
});

// GC 消息处理 - 使用正确的事件名称
client.on('receivedFromGC', (appid, msgType, payload) => {
    if (appid !== CSGO_APP_ID) return;

    logger.log(`收到 GC 消息，类型: ${msgType} (${payload.length} 字节)`, 'SUCCESS');

    switch (msgType) {
        case 4004: // ClientWelcome
            logger.log('收到 ClientWelcome 消息', 'SUCCESS');
            break;
        case 4005: // ServerWelcome
            logger.log('收到 ServerWelcome 消息', 'SUCCESS');
            break;
        case 4007: // ServerHello
            logger.log('收到 ServerHello 消息', 'SUCCESS');
            break;
        case MSG_TYPES.ITEM_CUSTOMIZATION:
            handleItemCustomization(payload);
            break;
        case MSG_TYPES.UNLOCK_CRATE_RESPONSE:
            handleUnlockCrateResponse(payload);
            break;
        case MSG_TYPES.USE_ITEM_RESPONSE:
            handleUseItemResponse(payload);
            break;
        default:
            if (msgType >= 1000 && msgType <= 5000) {
                logger.log(`收到 GC 消息: ${msgType}`, 'INFO');
            }
            break;
    }
});

// 处理物品掉落通知
function handleItemCustomization(payload) {
    try {
        const decoded = ItemCustomization.decode(payload);
        
        logger.log('收到物品掉落通知', 'CRATE');
        
        if (decoded.item_id && decoded.item_id.length > 0) {
            decoded.item_id.forEach((id, index) => {
                logger.log(`掉落物品 ${index + 1} 资产ID: ${id}`, 'SUCCESS');
                
                // 检查是否是我们正在跟踪的开箱
                for (let [crateKey, crateInfo] of openingCrates) {
                    if (Date.now() - crateInfo.timestamp < 60000) {
                        crateInfo.droppedItems.push(id);
                        logger.log(`开箱 ${crateKey} 获得物品: ${id}`, 'CRATE');
                    }
                }
            });
        }

        if (decoded.extra_data && decoded.extra_data.length > 0) {
            decoded.extra_data.forEach((data, index) => {
                logger.log(`额外数据 ${index + 1}: ${data}`, 'DEBUG');
            });
        }
        
    } catch (err) {
        logger.log(`解析物品掉落消息失败: ${err.message}`, 'ERROR');
    }
}

function handleUnlockCrateResponse(payload) {
    try {
        logger.log('收到开箱响应', 'CRATE');
    } catch (err) {
        logger.log(`解析开箱响应失败: ${err.message}`, 'ERROR');
    }
}

function handleUseItemResponse(payload) {
    try {
        logger.log('收到使用物品响应', 'DEBUG');
    } catch (err) {
        logger.log(`解析使用物品响应失败: ${err.message}`, 'ERROR');
    }
}

// 开箱核心类
class CrateOpener {
    constructor(client, logger, config) {
        this.client = client;
        this.logger = logger;
        this.config = config;
        this.openingCrates = new Map();
    }
    
    async unlockCrate(keyId, caseId, options = {}) {
        const crateKey = `${keyId}_${caseId}`;
        
        if (!isConnectedToGC) {
            this.logger.log('未连接到游戏协调器，等待连接...', 'WARNING');
            
            let waitTime = 0;
            while (!isConnectedToGC && waitTime < this.config.app.gcConnectionTimeout) {
                await delay(1000);
                waitTime += 1000;
            }
            
            if (!isConnectedToGC) {
                this.logger.log('连接游戏协调器超时', 'ERROR');
                return { success: false, error: '连接游戏协调器超时' };
            }
        }

        try {
            const crateInfo = {
                keyId,
                caseId,
                timestamp: Date.now(),
                droppedItems: [],
                retries: 0
            };
            
            this.openingCrates.set(crateKey, crateInfo);
            openingCrates.set(crateKey, crateInfo); // 全局引用
            
            this.logger.log(`开始开箱 - 钥匙ID: ${keyId}, 箱子ID: ${caseId}`, 'CRATE');
            
            const payload = {
                tool_item_id: keyId,
                subject_item_id: caseId,
                for_rental: options.forRental || false
            };
            
            const encodedPayload = OpenCrateMsg.encode(payload).finish();
            // 关键修正：使用 {} 而不是 null 作为 protobuf header
            this.client.sendToGC(CSGO_APP_ID, MSG_TYPES.OPEN_CRATE, {}, encodedPayload);
            
            this.logger.log(`开箱请求已发送`, 'SUCCESS');
            
            const result = await this.waitForCrateResult(crateKey, options.timeout || this.config.crate.timeout);
            return result;
            
        } catch (err) {
            this.logger.log(`开箱失败: ${err.message}`, 'ERROR');
            this.openingCrates.delete(crateKey);
            openingCrates.delete(crateKey);
            return { success: false, error: err.message };
        }
    }
    
    waitForCrateResult(crateKey, timeout = 30000) {
        return new Promise((resolve) => {
            const startTime = Date.now();
            
            const checkResult = () => {
                const crateInfo = this.openingCrates.get(crateKey);
                
                if (!crateInfo) {
                    resolve({ success: false, error: '开箱记录丢失' });
                    return;
                }
                
                if (crateInfo.droppedItems.length > 0) {
                    this.logger.log(`开箱成功！获得 ${crateInfo.droppedItems.length} 个物品`, 'SUCCESS');
                    this.openingCrates.delete(crateKey);
                    openingCrates.delete(crateKey);
                    resolve({ 
                        success: true, 
                        items: crateInfo.droppedItems,
                        duration: Date.now() - crateInfo.timestamp
                    });
                    return;
                }
                
                if (Date.now() - startTime > timeout) {
                    this.logger.log('开箱超时', 'ERROR');
                    this.openingCrates.delete(crateKey);
                    openingCrates.delete(crateKey);
                    resolve({ success: false, error: '开箱超时' });
                    return;
                }
                
                setTimeout(checkResult, 1000);
            };
            
            checkResult();
        });
    }
    
    async unlockMultipleCrates(crateList, options = {}) {
        const results = [];
        const batchDelay = options.batchDelay || this.config.crate.batchDelay;
        
        this.logger.log(`开始批量开箱，共 ${crateList.length} 个箱子`, 'CRATE');
        
        for (let i = 0; i < crateList.length; i++) {
            const { keyId, caseId } = crateList[i];
            
            this.logger.log(`批量开箱进度: ${i + 1}/${crateList.length}`, 'INFO');
            
            const result = await this.unlockCrate(keyId, caseId, options);
            results.push({ keyId, caseId, ...result });
            
            if (i < crateList.length - 1) {
                await delay(batchDelay);
            }
        }
        
        const successful = results.filter(r => r.success).length;
        const failed = results.length - successful;
        
        this.logger.log(`批量开箱完成 - 成功: ${successful}, 失败: ${failed}`, 'SUCCESS');
        
        return results;
    }
}

// 创建开箱器实例
const crateOpener = new CrateOpener(client, logger, config);

// 登录
client.logOn({
    accountName: config.steam.accountName,
    password: config.steam.password,
    twoFactorCode: require('steam-totp').getAuthCode(config.steam.sharedSecret)
});

// 导出主要功能
module.exports = {
    client,
    logger,
    crateOpener,
    unlockCrate: (keyId, caseId, options) => crateOpener.unlockCrate(keyId, caseId, options),
    unlockMultipleCrates: (crateList, options) => crateOpener.unlockMultipleCrates(crateList, options)
};

// 优雅关闭
process.on('SIGINT', () => {
    logger.log('正在关闭程序...', 'INFO');
    client.logOff();
    process.exit(0);
});
