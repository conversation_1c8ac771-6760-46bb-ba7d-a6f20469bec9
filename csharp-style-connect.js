// 基于 C# 代码的 GC 连接实现
const SteamUser = require('steam-user');
const protobuf = require('protobufjs');
const path = require('path');

// 加载配置
let config;
try {
    config = require('./config.js');
} catch (err) {
    console.error('❌ 配置文件不存在');
    process.exit(1);
}

const client = new SteamUser();
const CSGO_APP_ID = 730;

// 日志函数
function log(msg, type = 'INFO') {
    const time = new Date().toISOString();
    const emoji = { 
        INFO: '📝', 
        SUCCESS: '✅', 
        ERROR: '❌', 
        WARNING: '⚠️',
        DEBUG: '🔍',
        CRATE: '📦'
    }[type] || '📝';
    console.log(`[${time}] ${emoji} ${msg}`);
}

// GC 消息类型枚举 (对应 C# 中的 EGCBaseClientMsg)
const EGCBaseClientMsg = {
    k_EMsgGCClientHello: 4006,
    k_EMsgGCClientWelcome: 4004,
    k_EMsgGCServerHello: 4007,
    k_EMsgGCServerWelcome: 4005,
    k_EMsgGCClientConnectionStatus: 4001,
    k_EMsgGCServerConnectionStatus: 4002
};

// 连接状态
let gcConnected = false;
let helloSent = false;

// 加载 proto 文件
let ClientHelloType = null;
let OpenCrateType = null;

try {
    // 加载 GC 消息 proto
    const gcRoot = protobuf.loadSync(path.join(__dirname, "proto", "gc_messages.proto"));
    ClientHelloType = gcRoot.lookupType('CMsgClientHello');
    log('GC 消息 proto 加载成功', 'SUCCESS');
    
    // 加载开箱 proto
    const crateRoot = protobuf.loadSync(path.join(__dirname, "proto", "opencrate.proto"));
    OpenCrateType = crateRoot.lookupType('CMsgOpenCrate');
    log('开箱消息 proto 加载成功', 'SUCCESS');
    
} catch (err) {
    log(`Proto 文件加载失败: ${err.message}`, 'ERROR');
    process.exit(1);
}

// 创建 ClientHello 消息 (完全按照 C# 代码)
function createClientHelloMessage() {
    if (!ClientHelloType) {
        throw new Error('ClientHello proto 类型未加载');
    }
    
    // 对应 C# 代码: helloMsg.Body.version = 2000202;
    const helloData = {
        version: 2000202  // 这是关键！必须与 C# 代码一致
    };
    
    try {
        const encoded = ClientHelloType.encode(helloData).finish();
        log(`ClientHello 消息创建成功 (版本: ${helloData.version})`, 'SUCCESS');
        log(`消息大小: ${encoded.length} 字节`, 'DEBUG');
        return encoded;
    } catch (err) {
        log(`ClientHello 编码失败: ${err.message}`, 'ERROR');
        throw err;
    }
}

// 发送 ClientHello (对应 C# 代码的发送逻辑)
function sendClientHello() {
    if (helloSent) {
        log('ClientHello 已发送，跳过', 'DEBUG');
        return;
    }

    try {
        log('发送 ClientHello 消息...', 'INFO');

        const helloMessage = createClientHelloMessage();

        // 对应 C# 代码: 发送到 GC
        // 消息类型: EGCBaseClientMsg.k_EMsgGCClientHello (4006)
        client.sendToGC(CSGO_APP_ID, EGCBaseClientMsg.k_EMsgGCClientHello, null, helloMessage);

        helloSent = true;
        log('ClientHello 消息已发送', 'SUCCESS');
        log('等待游戏协调器响应...', 'INFO');

        // 设置响应超时检查
        setTimeout(() => {
            if (!gcConnected) {
                log('等待 GC 响应中... (10秒)', 'INFO');

                // 如果15秒后还没连接，尝试发送额外消息
                setTimeout(() => {
                    if (!gcConnected) {
                        log('尝试发送额外的连接消息', 'WARNING');
                        try {
                            client.sendToGC(CSGO_APP_ID, EGCBaseClientMsg.k_EMsgGCClientWelcome, null, Buffer.alloc(0));
                        } catch (err) {
                            log(`发送额外消息失败: ${err.message}`, 'ERROR');
                        }
                    }
                }, 15000);
            }
        }, 10000);

    } catch (err) {
        log(`发送 ClientHello 失败: ${err.message}`, 'ERROR');
        throw err;
    }
}

// Steam 事件处理
client.on('loggedOn', () => {
    log('Steam 登录成功', 'SUCCESS');
    log(`Steam ID: ${client.steamID}`, 'INFO');
    
    // 启动 CSGO
    client.gamesPlayed([CSGO_APP_ID]);
});

client.on('appLaunched', (appid) => {
    if (appid === CSGO_APP_ID) {
        log('CSGO 启动成功', 'SUCCESS');
        
        // 等待几秒后发送 ClientHello
        setTimeout(() => {
            sendClientHello();
        }, 3000);
    }
});

client.on('gcConnected', (appid) => {
    if (appid === CSGO_APP_ID) {
        gcConnected = true;
        log('🎉 游戏协调器连接成功！', 'SUCCESS');
        
        // 连接成功后可以进行开箱
        setTimeout(() => {
            log('=== GC 连接完成，可以开始开箱 ===', 'SUCCESS');
            // 这里可以调用开箱函数
            // testOpenCrate();
        }, 2000);
    }
});

client.on('gcDisconnected', (appid) => {
    if (appid === CSGO_APP_ID) {
        gcConnected = false;
        helloSent = false; // 重置状态
        log('GC 连接断开', 'WARNING');
        
        // 尝试重连
        setTimeout(() => {
            if (!gcConnected) {
                log('尝试重新连接 GC', 'INFO');
                sendClientHello();
            }
        }, 5000);
    }
});

client.on('gcMessage', (appid, msgType, proto) => {
    if (appid !== CSGO_APP_ID) return;
    
    log(`收到 GC 消息: ${msgType}`, 'DEBUG');
    
    // 处理特定消息类型
    switch (msgType) {
        case EGCBaseClientMsg.k_EMsgGCClientWelcome: // 4004
            log('收到 ClientWelcome 消息', 'SUCCESS');
            break;
            
        case EGCBaseClientMsg.k_EMsgGCServerWelcome: // 4005
            log('收到 ServerWelcome 消息', 'SUCCESS');
            break;
            
        case EGCBaseClientMsg.k_EMsgGCServerHello: // 4007
            log('收到 ServerHello 消息', 'SUCCESS');
            break;
            
        case 1090: // 物品掉落通知
            log('收到物品掉落消息', 'CRATE');
            handleItemDrop(proto);
            break;
            
        case 1008: // 开箱响应
            log('收到开箱响应', 'CRATE');
            break;
            
        default:
            if (msgType >= 1000 && msgType <= 5000) {
                log(`未处理的 GC 消息: ${msgType}`, 'DEBUG');
            }
            break;
    }
});

client.on('error', (err) => {
    log(`Steam 客户端错误: ${err.message}`, 'ERROR');
});

// 处理物品掉落
function handleItemDrop(proto) {
    try {
        // 这里需要解析物品掉落消息
        log('解析物品掉落消息...', 'DEBUG');
        // 具体解析逻辑需要对应的 proto 定义
    } catch (err) {
        log(`解析物品掉落失败: ${err.message}`, 'ERROR');
    }
}

// 测试开箱函数
async function testOpenCrate(keyId, caseId) {
    if (!gcConnected) {
        log('GC 未连接，无法开箱', 'ERROR');
        return false;
    }
    
    if (!OpenCrateType) {
        log('开箱 proto 未加载', 'ERROR');
        return false;
    }
    
    try {
        log(`开始开箱 - 钥匙: ${keyId}, 箱子: ${caseId}`, 'CRATE');
        
        // 构建开箱消息
        const openCrateData = {
            tool_item_id: keyId,
            subject_item_id: caseId,
            for_rental: false
        };
        
        const encoded = OpenCrateType.encode(openCrateData).finish();
        
        // 发送开箱请求 (消息类型 2534)
        client.sendToGC(CSGO_APP_ID, 2534, null, encoded);
        
        log('开箱请求已发送', 'SUCCESS');
        return true;
        
    } catch (err) {
        log(`开箱失败: ${err.message}`, 'ERROR');
        return false;
    }
}

// 主函数
async function main() {
    log('=== 基于 C# 代码的 GC 连接测试 ===', 'INFO');
    log('版本号: 2000202 (与 C# 代码一致)', 'INFO');
    
    // 登录 Steam
    client.logOn({
        accountName: config.steam.accountName,
        password: config.steam.password,
        twoFactorCode: require('steam-totp').getAuthCode(config.steam.sharedSecret)
    });
    
    // 设置总超时
    setTimeout(() => {
        if (!gcConnected) {
            log('连接超时失败', 'ERROR');
            log('请检查:', 'WARNING');
            log('1. 网络连接', 'INFO');
            log('2. CSGO 服务器状态', 'INFO');
            log('3. Steam 账号状态', 'INFO');
            process.exit(1);
        }
    }, 120000); // 2分钟总超时
}

// 优雅退出
process.on('SIGINT', () => {
    log('程序被中断', 'WARNING');
    client.logOff();
    process.exit(0);
});

// 导出函数
module.exports = {
    client,
    gcConnected: () => gcConnected,
    sendClientHello,
    testOpenCrate,
    log
};

// 如果直接运行
if (require.main === module) {
    main().catch(err => {
        console.error('程序错误:', err);
        process.exit(1);
    });
}
