// 调试版 GC 连接脚本
const SteamUser = require('steam-user');
const protobuf = require('protobufjs');
const path = require('path');

// 加载配置
let config;
try {
    config = require('./config.js');
} catch (err) {
    console.error('❌ 配置文件不存在');
    process.exit(1);
}

const client = new SteamUser();
const CSGO_APP_ID = 730;

// 详细日志
function log(msg, type = 'INFO') {
    const time = new Date().toISOString();
    const emoji = { 
        INFO: '📝', 
        SUCCESS: '✅', 
        ERROR: '❌', 
        WARNING: '⚠️',
        DEBUG: '🔍',
        NETWORK: '🌐'
    }[type] || '📝';
    console.log(`[${time}] ${emoji} ${msg}`);
}

// 连接状态跟踪
let connectionState = {
    steamLoggedIn: false,
    gameStarted: false,
    gcConnected: false,
    helloSent: false,
    messagesReceived: [],
    connectionAttempts: 0
};

// 加载 proto
let ClientHelloType = null;
try {
    const gcRoot = protobuf.loadSync(path.join(__dirname, "proto", "gc_messages.proto"));
    ClientHelloType = gcRoot.lookupType('CMsgClientHello');
    log('Proto 文件加载成功', 'SUCCESS');
} catch (err) {
    log(`Proto 文件加载失败: ${err.message}`, 'ERROR');
}

// 创建不同版本的 ClientHello 消息
function createClientHelloVariants() {
    const variants = [];
    
    if (ClientHelloType) {
        // 变体1: 完整消息
        variants.push({
            name: '完整 ClientHello',
            data: ClientHelloType.encode({
                version: 2000202,
                socache_have_versions: [],
                client_session_need: 1,
                client_launcher: 0,
                steam_launcher: 1
            }).finish()
        });
        
        // 变体2: 仅版本号
        variants.push({
            name: '仅版本号 ClientHello',
            data: ClientHelloType.encode({
                version: 2000202
            }).finish()
        });
        
        // 变体3: 不同版本号
        variants.push({
            name: '旧版本号 ClientHello',
            data: ClientHelloType.encode({
                version: 18
            }).finish()
        });
    }
    
    // 变体4: 空消息
    variants.push({
        name: '空 ClientHello',
        data: Buffer.alloc(0)
    });
    
    return variants;
}

// 尝试所有连接方法
async function tryAllConnectionMethods() {
    connectionState.connectionAttempts++;
    log(`开始第 ${connectionState.connectionAttempts} 次连接尝试`, 'INFO');
    
    const variants = createClientHelloVariants();
    
    for (let i = 0; i < variants.length; i++) {
        const variant = variants[i];
        
        if (connectionState.gcConnected) {
            log('已连接，停止尝试', 'SUCCESS');
            break;
        }
        
        log(`尝试 ${variant.name} (${variant.data.length} 字节)`, 'NETWORK');
        
        try {
            // 发送 ClientHello (4006)
            client.sendToGC(CSGO_APP_ID, 4006, null, variant.data);
            
            // 等待响应
            await new Promise(resolve => setTimeout(resolve, 3000));
            
            if (connectionState.gcConnected) {
                log(`成功！${variant.name} 有效`, 'SUCCESS');
                break;
            }
            
            // 尝试其他消息类型
            log('尝试其他消息类型', 'NETWORK');
            client.sendToGC(CSGO_APP_ID, 4004, null, Buffer.alloc(0)); // ClientWelcome
            await new Promise(resolve => setTimeout(resolve, 2000));
            
            if (connectionState.gcConnected) {
                log(`成功！ClientWelcome 有效`, 'SUCCESS');
                break;
            }
            
            client.sendToGC(CSGO_APP_ID, 4001, null, Buffer.alloc(0)); // ConnectionStatus
            await new Promise(resolve => setTimeout(resolve, 2000));
            
            if (connectionState.gcConnected) {
                log(`成功！ConnectionStatus 有效`, 'SUCCESS');
                break;
            }
            
        } catch (err) {
            log(`${variant.name} 失败: ${err.message}`, 'ERROR');
        }
    }
    
    if (!connectionState.gcConnected && connectionState.connectionAttempts < 3) {
        log('所有方法失败，5秒后重试', 'WARNING');
        setTimeout(() => {
            tryAllConnectionMethods();
        }, 5000);
    } else if (!connectionState.gcConnected) {
        log('达到最大重试次数，连接失败', 'ERROR');
        showDiagnostics();
    }
}

// 显示诊断信息
function showDiagnostics() {
    log('=== 连接诊断信息 ===', 'INFO');
    log(`Steam 登录: ${connectionState.steamLoggedIn}`, 'INFO');
    log(`游戏启动: ${connectionState.gameStarted}`, 'INFO');
    log(`GC 连接: ${connectionState.gcConnected}`, 'INFO');
    log(`Hello 发送: ${connectionState.helloSent}`, 'INFO');
    log(`连接尝试: ${connectionState.connectionAttempts}`, 'INFO');
    log(`收到消息: ${connectionState.messagesReceived.length}`, 'INFO');
    
    if (connectionState.messagesReceived.length > 0) {
        log('收到的消息类型:', 'INFO');
        connectionState.messagesReceived.forEach(msg => {
            log(`  - ${msg}`, 'INFO');
        });
    }
    
    log('=== 可能的原因 ===', 'WARNING');
    log('1. CSGO 服务器维护中', 'INFO');
    log('2. 网络连接问题', 'INFO');
    log('3. Steam 账号问题', 'INFO');
    log('4. 防火墙阻止连接', 'INFO');
    log('5. 协议版本不匹配', 'INFO');
}

// Steam 事件处理
client.on('loggedOn', () => {
    connectionState.steamLoggedIn = true;
    log('Steam 登录成功', 'SUCCESS');
    log(`Steam ID: ${client.steamID}`, 'INFO');
    client.gamesPlayed([CSGO_APP_ID]);
});

client.on('appLaunched', (appid) => {
    if (appid === CSGO_APP_ID) {
        connectionState.gameStarted = true;
        log('CSGO 启动成功', 'SUCCESS');
        
        // 等待几秒后开始连接尝试
        setTimeout(() => {
            tryAllConnectionMethods();
        }, 3000);
    }
});

client.on('gcConnected', (appid) => {
    if (appid === CSGO_APP_ID) {
        connectionState.gcConnected = true;
        log('🎉 GC 连接成功！', 'SUCCESS');
        
        // 显示成功信息
        setTimeout(() => {
            log('=== 连接成功统计 ===', 'SUCCESS');
            log(`总尝试次数: ${connectionState.connectionAttempts}`, 'INFO');
            log(`收到消息数: ${connectionState.messagesReceived.length}`, 'INFO');
            log('现在可以进行开箱操作', 'SUCCESS');
        }, 2000);
    }
});

client.on('gcDisconnected', (appid) => {
    if (appid === CSGO_APP_ID) {
        connectionState.gcConnected = false;
        log('GC 连接断开', 'WARNING');
    }
});

client.on('gcMessage', (appid, msgType, proto) => {
    if (appid === CSGO_APP_ID) {
        connectionState.messagesReceived.push(msgType);
        log(`收到 GC 消息: ${msgType}`, 'SUCCESS');
        
        // 详细处理特定消息
        switch (msgType) {
            case 4004:
                log('  -> ClientWelcome 消息', 'SUCCESS');
                break;
            case 4005:
                log('  -> ServerWelcome 消息', 'SUCCESS');
                break;
            case 4007:
                log('  -> ServerHello 消息', 'SUCCESS');
                break;
            default:
                log(`  -> 未知消息类型: ${msgType}`, 'INFO');
                break;
        }
    }
});

client.on('error', (err) => {
    log(`Steam 错误: ${err.message}`, 'ERROR');
});

// 网络状态监控
client.on('disconnected', (eresult, msg) => {
    log(`Steam 断开连接: ${eresult} - ${msg}`, 'ERROR');
});

client.on('loggedOff', (eresult, msg) => {
    log(`Steam 登出: ${eresult} - ${msg}`, 'WARNING');
});

// 主函数
async function main() {
    log('=== 调试版 GC 连接测试 ===', 'INFO');
    log('将尝试多种连接方法和消息格式', 'INFO');
    
    // 登录
    client.logOn({
        accountName: config.steam.accountName,
        password: config.steam.password,
        twoFactorCode: require('steam-totp').getAuthCode(config.steam.sharedSecret)
    });
    
    // 总超时
    setTimeout(() => {
        if (!connectionState.gcConnected) {
            log('总体连接超时', 'ERROR');
            showDiagnostics();
            process.exit(1);
        }
    }, 180000); // 3分钟总超时
}

// 优雅退出
process.on('SIGINT', () => {
    log('程序被中断', 'WARNING');
    showDiagnostics();
    client.logOff();
    process.exit(0);
});

// 运行
if (require.main === module) {
    main().catch(err => {
        console.error('程序错误:', err);
        process.exit(1);
    });
}

module.exports = { main, showDiagnostics };
