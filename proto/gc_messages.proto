syntax = "proto2";

// GC 基础消息定义

message CMsgSOCacheHaveVersion {
    optional uint32 service = 1;
    optional uint64 version = 2;
}

message CMsgClientHello {
    optional uint32 version = 1;
    repeated CMsgSOCacheHaveVersion socache_have_versions = 2;
    optional uint32 client_session_need = 3;
    optional uint32 client_launcher = 4;
    optional uint32 partner_srcid = 5;
    optional uint32 partner_accountid = 6;
    optional uint32 partner_accountflags = 7;
    optional uint32 partner_accountbalance = 8;
    optional uint32 steam_launcher = 9;
}

message CMsgClientWelcome {
    optional uint32 version = 1;
    optional bytes game_data = 2;
    repeated CMsgSOCacheHaveVersion socache_have_versions = 3;
    optional uint32 uptodate_subscribed_type = 4;
    optional uint32 location = 5;
    optional bytes generic_reply = 6;
}

// GC 消息类型枚举
enum EGCBaseClientMsg {
    k_EMsgGCClientConnectionStatus = 4001;
    k_EMsgGCServerConnectionStatus = 4002;
    k_EMsgGCClientHello = 4006;
    k_EMsgGCServerHello = 4007;
    k_EMsgGCClientWelcome = 4004;
    k_EMsgGCServerWelcome = 4005;
}
