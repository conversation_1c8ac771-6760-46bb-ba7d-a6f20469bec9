syntax = "proto3";

// GC 基础消息定义

message CMsgSOCacheHaveVersion {
    uint32 service = 1;
    uint64 version = 2;
}

message CMsgClientHello {
    uint32 version = 1;
    repeated CMsgSOCacheHaveVersion socache_have_versions = 2;
    uint32 client_session_need = 3;
    uint32 client_launcher = 4;
    uint32 partner_srcid = 5;
    uint32 partner_accountid = 6;
    uint32 partner_accountflags = 7;
    uint32 partner_accountbalance = 8;
    uint32 steam_launcher = 9;
}

message CMsgClientWelcome {
    uint32 version = 1;
    bytes game_data = 2;
    repeated CMsgSOCacheHaveVersion socache_have_versions = 3;
    uint32 uptodate_subscribed_type = 4;
    uint32 location = 5;
    bytes generic_reply = 6;
}

// GC 消息类型枚举
enum EGCBaseClientMsg {
    k_EMsgGCClientConnectionStatus = 4001;
    k_EMsgGCServerConnectionStatus = 4002;
    k_EMsgGCClientHello = 4006;
    k_EMsgGCServerHello = 4007;
    k_EMsgGCClientWelcome = 4004;
    k_EMsgGCServerWelcome = 4005;
}
