package google.protobuf;

option optimize_for = SPEED;
option cc_enable_arenas = true;
option go_package = "google.golang.org/protobuf/types/descriptorpb";
option java_package = "com.google.protobuf";
option java_outer_classname = "DescriptorProtos";
option objc_class_prefix = "GPB";
option csharp_namespace = "Google.Protobuf.Reflection";

message FileDescriptorSet {
	repeated .google.protobuf.FileDescriptorProto file = 1;
}

message FileDescriptorProto {
	optional string name = 1;
	optional string package = 2;
	repeated string dependency = 3;
	repeated int32 public_dependency = 10;
	repeated int32 weak_dependency = 11;
	repeated .google.protobuf.DescriptorProto message_type = 4;
	repeated .google.protobuf.EnumDescriptorProto enum_type = 5;
	repeated .google.protobuf.ServiceDescriptorProto service = 6;
	repeated .google.protobuf.FieldDescriptorProto extension = 7;
	optional .google.protobuf.FileOptions options = 8;
	optional .google.protobuf.SourceCodeInfo source_code_info = 9;
	optional string syntax = 12;
}

message DescriptorProto {
	message ExtensionRange {
		optional int32 start = 1;
		optional int32 end = 2;
		optional .google.protobuf.ExtensionRangeOptions options = 3;
	}

	message ReservedRange {
		optional int32 start = 1;
		optional int32 end = 2;
	}

	optional string name = 1;
	repeated .google.protobuf.FieldDescriptorProto field = 2;
	repeated .google.protobuf.FieldDescriptorProto extension = 6;
	repeated .google.protobuf.DescriptorProto nested_type = 3;
	repeated .google.protobuf.EnumDescriptorProto enum_type = 4;
	repeated .google.protobuf.DescriptorProto.ExtensionRange extension_range = 5;
	repeated .google.protobuf.OneofDescriptorProto oneof_decl = 8;
	optional .google.protobuf.MessageOptions options = 7;
	repeated .google.protobuf.DescriptorProto.ReservedRange reserved_range = 9;
	repeated string reserved_name = 10;
}

message ExtensionRangeOptions {
	repeated .google.protobuf.UninterpretedOption uninterpreted_option = 999;

	extensions 1000 to max;
}

message FieldDescriptorProto {
	enum Type {
		TYPE_DOUBLE = 1;
		TYPE_FLOAT = 2;
		TYPE_INT64 = 3;
		TYPE_UINT64 = 4;
		TYPE_INT32 = 5;
		TYPE_FIXED64 = 6;
		TYPE_FIXED32 = 7;
		TYPE_BOOL = 8;
		TYPE_STRING = 9;
		TYPE_GROUP = 10;
		TYPE_MESSAGE = 11;
		TYPE_BYTES = 12;
		TYPE_UINT32 = 13;
		TYPE_ENUM = 14;
		TYPE_SFIXED32 = 15;
		TYPE_SFIXED64 = 16;
		TYPE_SINT32 = 17;
		TYPE_SINT64 = 18;
	}

	enum Label {
		LABEL_OPTIONAL = 1;
		LABEL_REQUIRED = 2;
		LABEL_REPEATED = 3;
	}

	optional string name = 1;
	optional int32 number = 3;
	optional .google.protobuf.FieldDescriptorProto.Label label = 4 [default = LABEL_OPTIONAL];
	optional .google.protobuf.FieldDescriptorProto.Type type = 5 [default = TYPE_DOUBLE];
	optional string type_name = 6;
	optional string extendee = 2;
	optional string default_value = 7;
	optional int32 oneof_index = 9;
	optional string json_name = 10;
	optional .google.protobuf.FieldOptions options = 8;
	optional bool proto3_optional = 17;
}

message OneofDescriptorProto {
	optional string name = 1;
	optional .google.protobuf.OneofOptions options = 2;
}

message EnumDescriptorProto {
	message EnumReservedRange {
		optional int32 start = 1;
		optional int32 end = 2;
	}

	optional string name = 1;
	repeated .google.protobuf.EnumValueDescriptorProto value = 2;
	optional .google.protobuf.EnumOptions options = 3;
	repeated .google.protobuf.EnumDescriptorProto.EnumReservedRange reserved_range = 4;
	repeated string reserved_name = 5;
}

message EnumValueDescriptorProto {
	optional string name = 1;
	optional int32 number = 2;
	optional .google.protobuf.EnumValueOptions options = 3;
}

message ServiceDescriptorProto {
	optional string name = 1;
	repeated .google.protobuf.MethodDescriptorProto method = 2;
	optional .google.protobuf.ServiceOptions options = 3;
}

message MethodDescriptorProto {
	optional string name = 1;
	optional string input_type = 2;
	optional string output_type = 3;
	optional .google.protobuf.MethodOptions options = 4;
	optional bool client_streaming = 5 [default = false];
	optional bool server_streaming = 6 [default = false];
}

message FileOptions {
	enum OptimizeMode {
		SPEED = 1;
		CODE_SIZE = 2;
		LITE_RUNTIME = 3;
	}

	optional string java_package = 1;
	optional string java_outer_classname = 8;
	optional bool java_multiple_files = 10 [default = false];
	optional bool java_generate_equals_and_hash = 20 [deprecated = true];
	optional bool java_string_check_utf8 = 27 [default = false];
	optional .google.protobuf.FileOptions.OptimizeMode optimize_for = 9 [default = SPEED];
	optional string go_package = 11;
	optional bool cc_generic_services = 16 [default = false];
	optional bool java_generic_services = 17 [default = false];
	optional bool py_generic_services = 18 [default = false];
	optional bool php_generic_services = 42 [default = false];
	optional bool deprecated = 23 [default = false];
	optional bool cc_enable_arenas = 31 [default = true];
	optional string objc_class_prefix = 36;
	optional string csharp_namespace = 37;
	optional string swift_prefix = 39;
	optional string php_class_prefix = 40;
	optional string php_namespace = 41;
	optional string php_metadata_namespace = 44;
	optional string ruby_package = 45;
	repeated string additional_includes = 50;
	repeated .google.protobuf.UninterpretedOption uninterpreted_option = 999;

	extensions 1000 to max;
}

message MessageOptions {
	optional bool message_set_wire_format = 1 [default = false];
	optional bool no_standard_descriptor_accessor = 2 [default = false];
	optional bool deprecated = 3 [default = false];
	optional bool map_entry = 7;
	repeated .google.protobuf.UninterpretedOption uninterpreted_option = 999;

	extensions 1000 to max;
}

message FieldOptions {
	enum CType {
		STRING = 0;
		CORD = 1;
		STRING_PIECE = 2;
	}

	enum JSType {
		JS_NORMAL = 0;
		JS_STRING = 1;
		JS_NUMBER = 2;
	}

	optional .google.protobuf.FieldOptions.CType ctype = 1 [default = STRING];
	optional bool packed = 2;
	optional .google.protobuf.FieldOptions.JSType jstype = 6 [default = JS_NORMAL];
	optional bool lazy = 5 [default = false];
	optional bool deprecated = 3 [default = false];
	optional bool weak = 10 [default = false];
	optional string boxed_type = 20;
	repeated .google.protobuf.UninterpretedOption uninterpreted_option = 999;

	extensions 1000 to max;
}

message OneofOptions {
	repeated .google.protobuf.UninterpretedOption uninterpreted_option = 999;

	extensions 1000 to max;
}

message EnumOptions {
	optional bool allow_alias = 2;
	optional bool deprecated = 3 [default = false];
	repeated .google.protobuf.UninterpretedOption uninterpreted_option = 999;

	extensions 1000 to max;
}

message EnumValueOptions {
	optional bool deprecated = 1 [default = false];
	repeated .google.protobuf.UninterpretedOption uninterpreted_option = 999;

	extensions 1000 to max;
}

message ServiceOptions {
	optional bool deprecated = 33 [default = false];
	repeated .google.protobuf.UninterpretedOption uninterpreted_option = 999;

	extensions 1000 to max;
}

message MethodOptions {
	enum IdempotencyLevel {
		IDEMPOTENCY_UNKNOWN = 0;
		NO_SIDE_EFFECTS = 1;
		IDEMPOTENT = 2;
	}

	optional bool deprecated = 33 [default = false];
	optional .google.protobuf.MethodOptions.IdempotencyLevel idempotency_level = 34 [default = IDEMPOTENCY_UNKNOWN];
	repeated .google.protobuf.UninterpretedOption uninterpreted_option = 999;

	extensions 1000 to max;
}

message UninterpretedOption {
	message NamePart {
		required string name_part = 1;
		required bool is_extension = 2;
	}

	repeated .google.protobuf.UninterpretedOption.NamePart name = 2;
	optional string identifier_value = 3;
	optional uint64 positive_int_value = 4;
	optional int64 negative_int_value = 5;
	optional double double_value = 6;
	optional bytes string_value = 7;
	optional string aggregate_value = 8;
}

message SourceCodeInfo {
	message Location {
		repeated int32 path = 1 [packed = true];
		repeated int32 span = 2 [packed = true];
		optional string leading_comments = 3;
		optional string trailing_comments = 4;
		repeated string leading_detached_comments = 6;
	}

	repeated .google.protobuf.SourceCodeInfo.Location location = 1;
}

message GeneratedCodeInfo {
	message Annotation {
		repeated int32 path = 1 [packed = true];
		optional string source_file = 2;
		optional int32 begin = 3;
		optional int32 end = 4;
	}

	repeated .google.protobuf.GeneratedCodeInfo.Annotation annotation = 1;
}
