// 改进版开箱使用示例
const { unlockCrate, unlockMultipleCrates, logger, gcManager, waitForConnection } = require('./opencase-improved');

async function main() {
    try {
        logger.log('=== 开箱程序启动 ===', 'SUCCESS');
        
        // 等待连接到游戏协调器（最多等待60秒）
        logger.log('等待连接到游戏协调器...', 'INFO');
        await waitForConnection(60000);
        
        logger.log('游戏协调器连接成功，开始开箱测试', 'SUCCESS');
        
        // 额外等待几秒确保连接稳定
        await new Promise(resolve => setTimeout(resolve, 3000));
        
        // 示例1: 单个开箱
        logger.log('=== 单个开箱示例 ===', 'INFO');
        
        try {
            const singleResult = await unlockCrate(
                44104035671,  // 钥匙ID - 请替换为你的实际ID
                44615519345,  // 箱子ID - 请替换为你的实际ID
                {
                    timeout: 45000,           // 45秒超时
                    connectionTimeout: 30000  // 连接超时30秒
                }
            );
            
            if (singleResult.success) {
                logger.log(`开箱成功！`, 'SUCCESS');
                logger.log(`获得物品: ${singleResult.items.join(', ')}`, 'CRATE');
                logger.log(`耗时: ${singleResult.duration}ms`, 'INFO');
            } else {
                logger.log(`开箱失败: ${singleResult.error}`, 'ERROR');
            }
        } catch (error) {
            logger.log(`单个开箱异常: ${error.message}`, 'ERROR');
        }
        
        // 等待一段时间再进行批量开箱
        logger.log('等待5秒后进行批量开箱测试...', 'INFO');
        await new Promise(resolve => setTimeout(resolve, 5000));
        
        // 示例2: 批量开箱
        logger.log('=== 批量开箱示例 ===', 'INFO');
        
        const crateList = [
            { keyId: 44104035671, caseId: 44615519345 },
            { keyId: 44104035672, caseId: 44615519346 },
            { keyId: 44104035673, caseId: 44615519347 }
        ];
        
        try {
            const batchResults = await unlockMultipleCrates(crateList, {
                batchDelay: 3000,         // 每次开箱间隔3秒
                timeout: 45000,           // 每个箱子45秒超时
                connectionTimeout: 30000  // 连接超时30秒
            });
            
            // 分析结果
            const successfulCrates = batchResults.filter(r => r.success);
            const failedCrates = batchResults.filter(r => !r.success);
            
            logger.log(`=== 批量开箱结果统计 ===`, 'INFO');
            logger.log(`总数: ${batchResults.length}`, 'INFO');
            logger.log(`成功: ${successfulCrates.length}`, 'SUCCESS');
            logger.log(`失败: ${failedCrates.length}`, 'ERROR');
            
            // 显示获得的所有物品
            const allItems = successfulCrates.flatMap(r => r.items || []);
            if (allItems.length > 0) {
                logger.log(`=== 获得的物品 ===`, 'CRATE');
                allItems.forEach((item, index) => {
                    logger.log(`物品 ${index + 1}: ${item}`, 'SUCCESS');
                });
            }
            
            // 显示失败的详情
            if (failedCrates.length > 0) {
                logger.log(`=== 失败详情 ===`, 'ERROR');
                failedCrates.forEach((failed, index) => {
                    logger.log(`失败 ${index + 1}: 钥匙${failed.keyId}, 箱子${failed.caseId} - ${failed.error}`, 'ERROR');
                });
            }
            
            // 计算总耗时
            const totalDuration = successfulCrates.reduce((sum, r) => sum + (r.duration || 0), 0);
            if (totalDuration > 0) {
                logger.log(`成功开箱总耗时: ${totalDuration}ms (平均: ${Math.round(totalDuration / successfulCrates.length)}ms)`, 'INFO');
            }
            
        } catch (error) {
            logger.log(`批量开箱异常: ${error.message}`, 'ERROR');
        }
        
        logger.log('=== 开箱测试完成 ===', 'SUCCESS');
        
    } catch (error) {
        logger.log(`程序执行错误: ${error.message}`, 'ERROR');
        
        // 显示连接状态信息
        const stats = gcManager.getStats();
        logger.log(`连接状态: ${JSON.stringify(stats)}`, 'INFO');
        
        // 如果是连接问题，提供一些建议
        if (error.message.includes('连接') || error.message.includes('超时')) {
            logger.log('=== 连接问题排查建议 ===', 'WARNING');
            logger.log('1. 检查网络连接是否正常', 'INFO');
            logger.log('2. 确认 CSGO 服务器状态', 'INFO');
            logger.log('3. 尝试重启 Steam 客户端', 'INFO');
            logger.log('4. 检查防火墙设置', 'INFO');
            logger.log('5. 稍后再试', 'INFO');
        }
    }
}

// 处理未捕获的异常
process.on('unhandledRejection', (reason, promise) => {
    logger.log(`未处理的 Promise 拒绝: ${reason}`, 'ERROR');
});

process.on('uncaughtException', (error) => {
    logger.log(`未捕获的异常: ${error.message}`, 'ERROR');
    process.exit(1);
});

// 如果直接运行此文件
if (require.main === module) {
    main().catch(err => {
        console.error('程序错误:', err);
        process.exit(1);
    });
}

module.exports = { main };
