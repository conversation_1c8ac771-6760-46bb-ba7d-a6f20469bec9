// 简单有效的 GC 连接方法
const SteamUser = require('steam-user');
const protobuf = require('protobufjs');
const path = require('path');

// 加载配置
let config;
try {
    config = require('./config.js');
} catch (err) {
    console.error('❌ 配置文件不存在');
    process.exit(1);
}

const client = new SteamUser();
const CSGO_APP_ID = 730;

// 简单日志
function log(msg, type = 'INFO') {
    const time = new Date().toISOString();
    const emoji = { INFO: '📝', SUCCESS: '✅', ERROR: '❌', WARNING: '⚠️' }[type] || '📝';
    console.log(`[${time}] ${emoji} ${msg}`);
}

// 连接状态
let gcConnected = false;
let connectionTimer = null;

// 加载 ClientHello proto
let ClientHelloType = null;
try {
    const root = protobuf.loadSync(path.join(__dirname, "proto", "gc_messages.proto"));
    ClientHelloType = root.lookupType('CMsgClientHello');
    log('ClientHello proto 加载成功');
} catch (err) {
    log(`Proto 加载失败: ${err.message}`, 'WARNING');
}

// 创建正确的 ClientHello 消息
function createClientHelloMessage() {
    if (!ClientHelloType) {
        log('使用空消息作为 ClientHello', 'WARNING');
        return Buffer.alloc(0);
    }
    
    // 根据 C# 结构创建消息
    const helloData = {
        version: 18,                    // CSGO 客户端版本
        socache_have_versions: [],      // 空的缓存版本列表
        client_session_need: 1,         // 需要客户端会话
        client_launcher: 0,             // 客户端启动器类型
        partner_srcid: 0,              // 合作伙伴源ID
        partner_accountid: 0,          // 合作伙伴账户ID
        partner_accountflags: 0,       // 合作伙伴账户标志
        partner_accountbalance: 0,     // 合作伙伴账户余额
        steam_launcher: 1              // Steam 启动器
    };
    
    try {
        const encoded = ClientHelloType.encode(helloData).finish();
        log(`ClientHello 消息创建成功，大小: ${encoded.length} 字节`);
        return encoded;
    } catch (err) {
        log(`ClientHello 编码失败: ${err.message}`, 'ERROR');
        return Buffer.alloc(0);
    }
}

// 发送 GC 连接请求
function sendGCConnectionRequest() {
    if (gcConnected) {
        log('已连接到 GC');
        return;
    }
    
    try {
        log('发送 ClientHello 消息到 GC');
        const helloMessage = createClientHelloMessage();
        
        // 发送 ClientHello (消息类型 4006)
        client.sendToGC(CSGO_APP_ID, 4006, null, helloMessage);
        
        // 设置超时检查
        if (connectionTimer) {
            clearTimeout(connectionTimer);
        }
        
        connectionTimer = setTimeout(() => {
            if (!gcConnected) {
                log('GC 连接超时，尝试其他方法', 'WARNING');
                
                // 尝试发送其他消息
                log('发送 ClientWelcome 请求');
                client.sendToGC(CSGO_APP_ID, 4004, null, Buffer.alloc(0));
                
                setTimeout(() => {
                    if (!gcConnected) {
                        log('发送连接状态消息');
                        client.sendToGC(CSGO_APP_ID, 4001, null, Buffer.alloc(0));
                    }
                }, 2000);
            }
        }, 10000);
        
    } catch (err) {
        log(`发送 GC 连接请求失败: ${err.message}`, 'ERROR');
    }
}

// Steam 事件处理
client.on('loggedOn', () => {
    log('Steam 登录成功', 'SUCCESS');
    log(`Steam ID: ${client.steamID}`);
    
    // 启动 CSGO
    client.gamesPlayed([CSGO_APP_ID]);
});

client.on('appLaunched', (appid) => {
    if (appid === CSGO_APP_ID) {
        log('CSGO 启动成功', 'SUCCESS');
        
        // 等待几秒后尝试连接 GC
        setTimeout(() => {
            sendGCConnectionRequest();
        }, 3000);
    }
});

client.on('gcConnected', (appid) => {
    if (appid === CSGO_APP_ID) {
        gcConnected = true;
        log('🎉 GC 连接成功！', 'SUCCESS');
        
        if (connectionTimer) {
            clearTimeout(connectionTimer);
            connectionTimer = null;
        }
        
        // 连接成功后的处理
        setTimeout(() => {
            log('=== GC 连接测试完成 ===', 'SUCCESS');
            log('现在可以进行开箱操作了');
            
            // 可以在这里调用开箱函数
            // testOpenCrate();
            
        }, 2000);
    }
});

client.on('gcDisconnected', (appid) => {
    if (appid === CSGO_APP_ID) {
        gcConnected = false;
        log('GC 连接断开', 'WARNING');
        
        // 尝试重连
        setTimeout(() => {
            if (!gcConnected) {
                log('尝试重新连接 GC');
                sendGCConnectionRequest();
            }
        }, 5000);
    }
});

client.on('gcMessage', (appid, msgType, proto) => {
    if (appid === CSGO_APP_ID) {
        log(`收到 GC 消息: ${msgType}`);
        
        // 处理特定消息
        switch (msgType) {
            case 4004: // ClientWelcome
                log('收到 ClientWelcome', 'SUCCESS');
                break;
            case 4005: // ServerWelcome
                log('收到 ServerWelcome', 'SUCCESS');
                break;
            case 4007: // ServerHello
                log('收到 ServerHello', 'SUCCESS');
                break;
            case 1090: // 物品掉落
                log('收到物品掉落消息', 'SUCCESS');
                break;
        }
    }
});

client.on('error', (err) => {
    log(`Steam 错误: ${err.message}`, 'ERROR');
});

// 测试开箱函数（可选）
async function testOpenCrate() {
    if (!gcConnected) {
        log('GC 未连接，无法开箱', 'ERROR');
        return;
    }
    
    log('开始测试开箱...');
    
    // 这里可以添加开箱逻辑
    // 使用您提供的钥匙和箱子ID
    const keyId = 44104035671;
    const caseId = 44615519345;
    
    try {
        // 构建开箱消息
        const openCratePayload = {
            tool_item_id: keyId,
            subject_item_id: caseId,
            for_rental: false
        };
        
        // 这里需要加载 opencrate.proto 来编码消息
        // const encoded = OpenCrateMsg.encode(openCratePayload).finish();
        // client.sendToGC(CSGO_APP_ID, 2534, null, encoded);
        
        log('开箱请求已发送（示例）');
        
    } catch (err) {
        log(`开箱失败: ${err.message}`, 'ERROR');
    }
}

// 主函数
async function main() {
    log('=== 开始 GC 连接测试 ===');
    
    // 登录 Steam
    client.logOn({
        accountName: config.steam.accountName,
        password: config.steam.password,
        twoFactorCode: require('steam-totp').getAuthCode(config.steam.sharedSecret)
    });
    
    // 设置总超时
    setTimeout(() => {
        if (!gcConnected) {
            log('总体连接超时', 'ERROR');
            log('请检查网络连接和 CSGO 服务器状态');
            process.exit(1);
        }
    }, 120000); // 2分钟总超时
}

// 优雅退出
process.on('SIGINT', () => {
    log('程序被中断');
    if (connectionTimer) {
        clearTimeout(connectionTimer);
    }
    client.logOff();
    process.exit(0);
});

// 导出函数供其他模块使用
module.exports = {
    client,
    gcConnected: () => gcConnected,
    sendGCConnectionRequest,
    testOpenCrate
};

// 如果直接运行
if (require.main === module) {
    main().catch(err => {
        console.error('程序错误:', err);
        process.exit(1);
    });
}
