// 替代 GC 连接方法 - 基于不同的连接策略
const SteamUser = require('steam-user');
const protobuf = require('protobufjs');
const path = require('path');

// 加载配置
let config;
try {
    config = require('./config.js');
} catch (err) {
    console.error('❌ 配置文件不存在');
    process.exit(1);
}

const client = new SteamUser();
const CSGO_APP_ID = 730;

function log(msg, type = 'INFO') {
    const time = new Date().toISOString();
    const emoji = { INFO: '📝', SUCCESS: '✅', ERROR: '❌', WARNING: '⚠️', DEBUG: '🔍' }[type] || '📝';
    console.log(`[${time}] ${emoji} ${msg}`);
}

let gcConnected = false;
let connectionStrategy = 0;

// 策略1: 传统方法 - 直接发送 ClientHello
function strategy1_DirectClientHello() {
    log('策略1: 直接发送 ClientHello', 'INFO');
    
    try {
        // 尝试加载 proto
        const gcRoot = protobuf.loadSync(path.join(__dirname, "proto", "gc_messages.proto"));
        const ClientHelloType = gcRoot.lookupType('CMsgClientHello');
        
        const helloData = { version: 2000202 };
        const encoded = ClientHelloType.encode(helloData).finish();
        
        client.sendToGC(CSGO_APP_ID, 4006, null, encoded);
        log('ClientHello 已发送', 'SUCCESS');
    } catch (err) {
        log(`策略1 失败: ${err.message}`, 'ERROR');
        // 回退到空消息
        client.sendToGC(CSGO_APP_ID, 4006, null, Buffer.alloc(0));
    }
}

// 策略2: 先请求游戏数据
function strategy2_RequestGameData() {
    log('策略2: 先请求游戏数据', 'INFO');
    
    // 发送一些初始化消息
    setTimeout(() => {
        client.sendToGC(CSGO_APP_ID, 4004, null, Buffer.alloc(0)); // ClientWelcome
    }, 1000);
    
    setTimeout(() => {
        client.sendToGC(CSGO_APP_ID, 4001, null, Buffer.alloc(0)); // ConnectionStatus
    }, 2000);
    
    setTimeout(() => {
        strategy1_DirectClientHello(); // 然后发送 ClientHello
    }, 3000);
}

// 策略3: 模拟真实客户端行为
function strategy3_SimulateRealClient() {
    log('策略3: 模拟真实客户端行为', 'INFO');
    
    // 按照真实客户端的顺序发送消息
    const sequence = [
        { delay: 1000, msgType: 4001, data: Buffer.alloc(0), name: 'ConnectionStatus' },
        { delay: 2000, msgType: 4006, data: null, name: 'ClientHello' },
        { delay: 4000, msgType: 4004, data: Buffer.alloc(0), name: 'ClientWelcome' },
    ];
    
    sequence.forEach(msg => {
        setTimeout(() => {
            if (!gcConnected) {
                log(`发送 ${msg.name}`, 'DEBUG');
                
                if (msg.msgType === 4006) {
                    // 特殊处理 ClientHello
                    strategy1_DirectClientHello();
                } else {
                    client.sendToGC(CSGO_APP_ID, msg.msgType, null, msg.data);
                }
            }
        }, msg.delay);
    });
}

// 策略4: 使用不同的消息顺序
function strategy4_AlternativeSequence() {
    log('策略4: 使用不同的消息顺序', 'INFO');
    
    // 先发送 Welcome 请求
    client.sendToGC(CSGO_APP_ID, 4004, null, Buffer.alloc(0));
    
    setTimeout(() => {
        if (!gcConnected) {
            // 然后发送 Hello
            strategy1_DirectClientHello();
        }
    }, 2000);
    
    setTimeout(() => {
        if (!gcConnected) {
            // 最后发送状态请求
            client.sendToGC(CSGO_APP_ID, 4001, null, Buffer.alloc(0));
        }
    }, 4000);
}

// 策略5: 强制重新启动游戏
function strategy5_RestartGame() {
    log('策略5: 重新启动游戏', 'INFO');
    
    // 停止游戏
    client.gamesPlayed([]);
    
    setTimeout(() => {
        // 重新启动游戏
        client.gamesPlayed([CSGO_APP_ID]);
        
        setTimeout(() => {
            strategy1_DirectClientHello();
        }, 3000);
    }, 2000);
}

// 尝试所有策略
async function tryAllStrategies() {
    const strategies = [
        strategy1_DirectClientHello,
        strategy2_RequestGameData,
        strategy3_SimulateRealClient,
        strategy4_AlternativeSequence,
        strategy5_RestartGame
    ];
    
    for (let i = 0; i < strategies.length; i++) {
        if (gcConnected) {
            log(`策略 ${i + 1} 成功！`, 'SUCCESS');
            break;
        }
        
        connectionStrategy = i + 1;
        log(`尝试策略 ${connectionStrategy}`, 'INFO');
        
        try {
            strategies[i]();
            
            // 等待每个策略的结果
            await new Promise(resolve => setTimeout(resolve, 15000));
            
            if (gcConnected) {
                log(`策略 ${connectionStrategy} 成功连接！`, 'SUCCESS');
                break;
            } else {
                log(`策略 ${connectionStrategy} 失败`, 'WARNING');
            }
            
        } catch (err) {
            log(`策略 ${connectionStrategy} 异常: ${err.message}`, 'ERROR');
        }
    }
    
    if (!gcConnected) {
        log('所有策略都失败了', 'ERROR');
        showAlternativeSolutions();
    }
}

// 显示替代解决方案
function showAlternativeSolutions() {
    log('=== 替代解决方案 ===', 'WARNING');
    log('1. 检查 CSGO 是否需要更新', 'INFO');
    log('2. 尝试重启 Steam 客户端', 'INFO');
    log('3. 检查是否有 CSGO 正在运行', 'INFO');
    log('4. 尝试在 Steam 中手动启动 CSGO', 'INFO');
    log('5. 检查网络防火墙设置', 'INFO');
    log('6. 尝试使用 VPN', 'INFO');
    log('7. 等待 CSGO 服务器恢复正常', 'INFO');
}

// Steam 事件处理
client.on('loggedOn', () => {
    log('Steam 登录成功', 'SUCCESS');
    log(`Steam ID: ${client.steamID}`, 'INFO');
    client.gamesPlayed([CSGO_APP_ID]);
});

client.on('appLaunched', (appid) => {
    if (appid === CSGO_APP_ID) {
        log('CSGO 启动成功', 'SUCCESS');
        
        // 等待游戏完全加载
        setTimeout(() => {
            tryAllStrategies();
        }, 5000);
    }
});

client.on('gcConnected', (appid) => {
    if (appid === CSGO_APP_ID) {
        gcConnected = true;
        log(`🎉 GC 连接成功！使用策略 ${connectionStrategy}`, 'SUCCESS');
    }
});

client.on('gcDisconnected', (appid) => {
    if (appid === CSGO_APP_ID) {
        gcConnected = false;
        log('GC 连接断开', 'WARNING');
    }
});

client.on('gcMessage', (appid, msgType, proto) => {
    if (appid === CSGO_APP_ID) {
        log(`收到 GC 消息: ${msgType}`, 'SUCCESS');
        
        // 记录重要消息
        const importantMessages = {
            4004: 'ClientWelcome',
            4005: 'ServerWelcome', 
            4007: 'ServerHello',
            1090: 'ItemDrop'
        };
        
        if (importantMessages[msgType]) {
            log(`  -> ${importantMessages[msgType]}`, 'SUCCESS');
        }
    }
});

client.on('error', (err) => {
    log(`Steam 错误: ${err.message}`, 'ERROR');
});

// 主函数
async function main() {
    log('=== 替代 GC 连接方法测试 ===', 'INFO');
    log('将尝试5种不同的连接策略', 'INFO');
    
    client.logOn({
        accountName: config.steam.accountName,
        password: config.steam.password,
        twoFactorCode: require('steam-totp').getAuthCode(config.steam.sharedSecret)
    });
    
    // 总超时
    setTimeout(() => {
        if (!gcConnected) {
            log('所有策略都失败，连接超时', 'ERROR');
            showAlternativeSolutions();
            process.exit(1);
        }
    }, 300000); // 5分钟总超时
}

// 优雅退出
process.on('SIGINT', () => {
    log('程序被中断', 'WARNING');
    client.logOff();
    process.exit(0);
});

if (require.main === module) {
    main().catch(err => {
        console.error('程序错误:', err);
        process.exit(1);
    });
}

module.exports = { main };
