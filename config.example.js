// 配置文件示例
// 复制此文件为 config.js 并填入你的实际信息

module.exports = {
    // Steam 账号信息
    steam: {
        accountName: 'your_steam_username',
        password: 'your_steam_password',
        sharedSecret: 'your_shared_secret_for_2fa', // 用于生成两步验证码
    },
    
    // 开箱配置
    crate: {
        maxRetries: 3,           // 最大重试次数
        retryDelay: 5000,        // 重试延迟（毫秒）
        timeout: 30000,          // 开箱超时时间（毫秒）
        batchDelay: 2000,        // 批量开箱间隔（毫秒）
    },
    
    // 日志配置
    logging: {
        level: 'INFO',           // 日志级别: DEBUG, INFO, WARNING, ERROR
        saveToFile: false,       // 是否保存日志到文件
        logFile: 'opencase.log'  // 日志文件名
    },
    
    // 应用配置
    app: {
        csgoAppId: 730,          // CSGO 应用 ID
        gcConnectionTimeout: 30000, // GC 连接超时时间
    }
};
