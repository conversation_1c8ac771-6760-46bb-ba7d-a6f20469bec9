// 高级连接测试脚本
const SteamUser = require('steam-user');
const protobuf = require('protobufjs');
const path = require('path');

// 加载配置
let config;
try {
    config = require('./config.js');
} catch (err) {
    console.error('❌ 配置文件 config.js 不存在');
    process.exit(1);
}

const client = new SteamUser();
const CSGO_APP_ID = 730;

// 日志函数
function log(message, type = 'INFO') {
    const timestamp = new Date().toISOString();
    const prefix = {
        'INFO': '📝',
        'SUCCESS': '✅',
        'ERROR': '❌',
        'WARNING': '⚠️',
        'DEBUG': '🔍'
    }[type] || '📝';
    
    console.log(`[${timestamp}] ${prefix} ${message}`);
}

// 连接状态
let isLoggedIn = false;
let isGameLaunched = false;
let isGCConnected = false;
let connectionAttempts = 0;

// 加载 proto 文件
let ClientHello = null;
try {
    const gcRoot = protobuf.loadSync(path.join(__dirname, "proto", "gc_messages.proto"));
    ClientHello = gcRoot.lookupType('CMsgClientHello');
    log('Proto 文件加载成功', 'SUCCESS');
} catch (err) {
    log(`Proto 文件加载失败: ${err.message}`, 'WARNING');
}

// Steam 事件监听
client.on('loggedOn', () => {
    isLoggedIn = true;
    log('Steam 登录成功', 'SUCCESS');
    log(`Steam ID: ${client.steamID}`, 'INFO');
    
    // 启动游戏
    client.gamesPlayed([CSGO_APP_ID]);
});

client.on('appLaunched', (appid) => {
    if (appid === CSGO_APP_ID) {
        isGameLaunched = true;
        log('CSGO 游戏启动成功', 'SUCCESS');
        
        // 开始尝试连接 GC
        setTimeout(() => {
            attemptGCConnection();
        }, 3000);
    }
});

client.on('gcConnected', (appid) => {
    if (appid === CSGO_APP_ID) {
        isGCConnected = true;
        log('🎉 游戏协调器连接成功！', 'SUCCESS');
    }
});

client.on('gcDisconnected', (appid) => {
    if (appid === CSGO_APP_ID) {
        isGCConnected = false;
        log('游戏协调器连接断开', 'WARNING');
    }
});

client.on('gcMessage', (appid, msgType, proto) => {
    if (appid === CSGO_APP_ID) {
        log(`收到 GC 消息: ${msgType}`, 'DEBUG');
        
        // 特殊处理一些消息类型
        switch (msgType) {
            case 4004: // ClientWelcome
                log('收到 ClientWelcome 消息', 'SUCCESS');
                break;
            case 4005: // ServerWelcome  
                log('收到 ServerWelcome 消息', 'SUCCESS');
                break;
            case 4007: // ServerHello
                log('收到 ServerHello 消息', 'SUCCESS');
                break;
        }
    }
});

client.on('error', (err) => {
    log(`Steam 客户端错误: ${err.message}`, 'ERROR');
});

// 多种 GC 连接策略
const connectionStrategies = [
    {
        name: '标准 ClientHello',
        execute: () => {
            if (ClientHello) {
                const helloMessage = {
                    version: 18,
                    client_session_need: 1,
                    client_launcher: 0,
                    steam_launcher: 1
                };
                const payload = ClientHello.encode(helloMessage).finish();
                client.sendToGC(CSGO_APP_ID, 4006, null, payload);
            } else {
                client.sendToGC(CSGO_APP_ID, 4006, null, Buffer.alloc(0));
            }
        }
    },
    {
        name: '请求 ClientWelcome',
        execute: () => {
            client.sendToGC(CSGO_APP_ID, 4004, null, Buffer.alloc(0));
        }
    },
    {
        name: '连接状态消息',
        execute: () => {
            client.sendToGC(CSGO_APP_ID, 4001, null, Buffer.alloc(0));
        }
    },
    {
        name: '组合策略',
        execute: () => {
            // 先发送 ClientHello
            if (ClientHello) {
                const helloMessage = {
                    version: 18,
                    client_session_need: 1,
                    client_launcher: 0,
                    steam_launcher: 1
                };
                const payload = ClientHello.encode(helloMessage).finish();
                client.sendToGC(CSGO_APP_ID, 4006, null, payload);
            }
            
            // 然后发送其他消息
            setTimeout(() => {
                client.sendToGC(CSGO_APP_ID, 4004, null, Buffer.alloc(0));
            }, 1000);
            
            setTimeout(() => {
                client.sendToGC(CSGO_APP_ID, 4001, null, Buffer.alloc(0));
            }, 2000);
        }
    }
];

// 尝试 GC 连接
async function attemptGCConnection() {
    if (isGCConnected) {
        log('已连接到游戏协调器', 'INFO');
        return true;
    }
    
    connectionAttempts++;
    log(`开始第 ${connectionAttempts} 次连接尝试`, 'INFO');
    
    for (let i = 0; i < connectionStrategies.length; i++) {
        const strategy = connectionStrategies[i];
        
        log(`尝试策略 ${i + 1}: ${strategy.name}`, 'INFO');
        
        try {
            strategy.execute();
            
            // 等待连接结果
            const connected = await waitForConnection(10000);
            if (connected) {
                log(`策略 "${strategy.name}" 成功！`, 'SUCCESS');
                return true;
            }
            
        } catch (err) {
            log(`策略 "${strategy.name}" 失败: ${err.message}`, 'ERROR');
        }
        
        // 策略间等待
        if (i < connectionStrategies.length - 1) {
            await new Promise(resolve => setTimeout(resolve, 3000));
        }
    }
    
    log(`第 ${connectionAttempts} 次连接尝试失败`, 'ERROR');
    
    // 如果还没达到最大重试次数，继续尝试
    if (connectionAttempts < 5) {
        const delay = 5000 * connectionAttempts; // 递增延迟
        log(`${delay/1000} 秒后进行下一次尝试...`, 'INFO');
        setTimeout(() => {
            attemptGCConnection();
        }, delay);
    } else {
        log('达到最大重试次数，连接失败', 'ERROR');
        showDiagnostics();
    }
    
    return false;
}

// 等待连接
function waitForConnection(timeout) {
    return new Promise((resolve) => {
        if (isGCConnected) {
            resolve(true);
            return;
        }
        
        const timeoutId = setTimeout(() => {
            client.removeListener('gcConnected', onConnected);
            resolve(false);
        }, timeout);
        
        const onConnected = (appid) => {
            if (appid === CSGO_APP_ID) {
                clearTimeout(timeoutId);
                resolve(true);
            }
        };
        
        client.once('gcConnected', onConnected);
    });
}

// 显示诊断信息
function showDiagnostics() {
    log('=== 诊断信息 ===', 'INFO');
    log(`Steam 登录状态: ${isLoggedIn}`, 'INFO');
    log(`游戏启动状态: ${isGameLaunched}`, 'INFO');
    log(`GC 连接状态: ${isGCConnected}`, 'INFO');
    log(`连接尝试次数: ${connectionAttempts}`, 'INFO');
    
    if (client.steamID) {
        log(`Steam ID: ${client.steamID}`, 'INFO');
    }
    
    log('=== 建议 ===', 'WARNING');
    log('1. 检查网络连接', 'INFO');
    log('2. 检查 CSGO 服务器状态', 'INFO');
    log('3. 尝试重启 Steam', 'INFO');
    log('4. 检查防火墙设置', 'INFO');
}

// 主函数
async function main() {
    log('=== 高级连接测试开始 ===', 'INFO');
    
    // 登录 Steam
    client.logOn({
        accountName: config.steam.accountName,
        password: config.steam.password,
        twoFactorCode: require('steam-totp').getAuthCode(config.steam.sharedSecret)
    });
    
    // 等待最终结果
    setTimeout(() => {
        if (isGCConnected) {
            log('=== 连接测试成功 ===', 'SUCCESS');
            log('现在可以进行开箱操作了', 'INFO');
        } else {
            log('=== 连接测试失败 ===', 'ERROR');
            showDiagnostics();
        }
        
        process.exit(isGCConnected ? 0 : 1);
    }, 120000); // 2分钟总超时
}

// 处理程序退出
process.on('SIGINT', () => {
    log('测试被中断', 'WARNING');
    client.logOff();
    process.exit(0);
});

// 运行测试
if (require.main === module) {
    main().catch(err => {
        console.error('测试异常:', err);
        process.exit(1);
    });
}

module.exports = { main };
