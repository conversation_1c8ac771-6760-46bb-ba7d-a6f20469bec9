const SteamUser = require('steam-user');
const SteamID = require('steamid');
const protobuf = require('protobufjs');
const fs = require('fs');
const path = require("path");

// 你的 Steam 账号信息
const client = new SteamUser();

// CSGO AppID
const CSGO_APP_ID = 730;

// 消息类型常量
const MSG_TYPES = {
    OPEN_CRATE: 2534,
    UNLOCK_CRATE: 1007,
    UNLOCK_CRATE_RESPONSE: 1008,
    ITEM_CUSTOMIZATION: 1090,
    USE_ITEM_REQUEST: 1025,
    USE_ITEM_RESPONSE: 1026
};

// proto 文件
const root = protobuf.loadSync(
    path.join(__dirname, "proto", "econ_gcmessages.proto")
);

// 加载开箱消息结构
const openCrateRoot = protobuf.loadSync(
    path.join(__dirname, "proto", "opencrate.proto")
);

const ItemCustomization = root.lookupType('CMsgGCItemCustomizationNotification');
const OpenCrateMsg = openCrateRoot.lookupType('CMsgOpenCrate');

// 开箱状态跟踪
let openingCrates = new Map();
let isConnectedToGC = false;

// 配置信息 - 建议从环境变量或配置文件读取
const config = {
    accountName: 'an666459',
    password: '8imUXpPByQXs',
    sharedSecret: 'HsoQxtAzpSGsTVVXrsxku0+398M=',
    // 开箱配置
    maxRetries: 3,
    retryDelay: 5000, // 5秒
    timeout: 30000 // 30秒超时
};

// 工具函数
function log(message, type = 'INFO') {
    const timestamp = new Date().toISOString();
    const prefix = {
        'INFO': '📝',
        'SUCCESS': '✅',
        'ERROR': '❌',
        'WARNING': '⚠️',
        'CRATE': '📦'
    }[type] || '📝';

    console.log(`[${timestamp}] ${prefix} ${message}`);
}

function delay(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
}

// 登录 Steam
client.logOn({
    accountName: config.accountName,
    password: config.password,
    twoFactorCode: require('steam-totp').getAuthCode(config.sharedSecret)
});

client.on('loggedOn', () => {
    log('已登录 Steam', 'SUCCESS');

    // 告知 Steam 在玩 CSGO
    client.gamesPlayed([CSGO_APP_ID]);
});

client.on('playingState', (blocked, apps) => {
    log(`正在玩游戏状态: ${JSON.stringify(apps)}`, 'INFO');
});

// GC 连接状态监听
client.on('appLaunched', (appid) => {
    if (appid === CSGO_APP_ID) {
        log('CSGO 应用已启动', 'SUCCESS');
    }
});

client.on('gcConnected', (appid) => {
    if (appid === CSGO_APP_ID) {
        isConnectedToGC = true;
        log('已连接到 CSGO 游戏协调器', 'SUCCESS');
    }
});

client.on('gcDisconnected', (appid) => {
    if (appid === CSGO_APP_ID) {
        isConnectedToGC = false;
        log('与 CSGO 游戏协调器断开连接', 'WARNING');
    }
});

client.on('error', (err) => {
    log(`Steam 客户端错误: ${err.message}`, 'ERROR');
});

// 监听 GC 消息
client.on('gcMessage', (appid, msgType, proto) => {
    if (appid !== CSGO_APP_ID) return;

    log(`收到 GC 消息，类型: ${msgType}`, 'INFO');

    switch (msgType) {
        case MSG_TYPES.ITEM_CUSTOMIZATION: // 1090 - 物品掉落通知
            handleItemCustomization(proto);
            break;

        case MSG_TYPES.UNLOCK_CRATE_RESPONSE: // 1008 - 开箱响应
            handleUnlockCrateResponse(proto);
            break;

        case MSG_TYPES.USE_ITEM_RESPONSE: // 1026 - 使用物品响应
            handleUseItemResponse(proto);
            break;

        default:
            // 记录其他消息类型以便调试
            if (msgType >= 1000 && msgType <= 3000) {
                log(`未处理的 GC 消息类型: ${msgType}`, 'INFO');
            }
            break;
    }
});

// 处理物品掉落通知
function handleItemCustomization(proto) {
    try {
        const decoded = ItemCustomization.decode(proto);

        log('收到物品掉落通知', 'CRATE');

        if (decoded.item_id && decoded.item_id.length > 0) {
            decoded.item_id.forEach((id, index) => {
                log(`掉落物品 ${index + 1} 资产ID: ${id}`, 'SUCCESS');

                // 检查是否是我们正在跟踪的开箱
                for (let [crateKey, crateInfo] of openingCrates) {
                    if (Date.now() - crateInfo.timestamp < 60000) { // 1分钟内
                        crateInfo.droppedItems.push(id);
                        log(`开箱 ${crateKey} 获得物品: ${id}`, 'CRATE');
                    }
                }
            });
        }

        if (decoded.extra_data && decoded.extra_data.length > 0) {
            decoded.extra_data.forEach((data, index) => {
                log(`额外数据 ${index + 1}: ${data}`, 'INFO');
            });
        }

        if (decoded.request) {
            log(`请求信息: ${decoded.request}`, 'INFO');
        }

    } catch (err) {
        log(`解析物品掉落消息失败: ${err.message}`, 'ERROR');
    }
}

// 处理开箱响应
function handleUnlockCrateResponse(proto) {
    try {
        log('收到开箱响应', 'CRATE');
        // 这里可以根据需要解析响应数据
        // 通常开箱响应会包含成功/失败状态
    } catch (err) {
        log(`解析开箱响应失败: ${err.message}`, 'ERROR');
    }
}

// 处理使用物品响应
function handleUseItemResponse(proto) {
    try {
        log('收到使用物品响应', 'INFO');
        // 解析使用物品的响应
    } catch (err) {
        log(`解析使用物品响应失败: ${err.message}`, 'ERROR');
    }
}

// 改进的开箱方法
async function unlockCrate(keyId, caseId, options = {}) {
    const crateKey = `${keyId}_${caseId}`;

    // 检查是否已连接到 GC
    if (!isConnectedToGC) {
        log('未连接到游戏协调器，等待连接...', 'WARNING');

        // 等待连接，最多等待30秒
        let waitTime = 0;
        while (!isConnectedToGC && waitTime < 30000) {
            await delay(1000);
            waitTime += 1000;
        }

        if (!isConnectedToGC) {
            log('连接游戏协调器超时', 'ERROR');
            return { success: false, error: '连接游戏协调器超时' };
        }
    }

    try {
        // 创建开箱记录
        const crateInfo = {
            keyId,
            caseId,
            timestamp: Date.now(),
            droppedItems: [],
            retries: 0
        };

        openingCrates.set(crateKey, crateInfo);

        log(`开始开箱 - 钥匙ID: ${keyId}, 箱子ID: ${caseId}`, 'CRATE');

        // 构建消息载荷
        const payload = {
            tool_item_id: keyId,
            subject_item_id: caseId,
            for_rental: options.forRental || false
        };

        // 编码消息
        const encodedPayload = OpenCrateMsg.encode(payload).finish();

        // 发送开箱请求
        client.sendToGC(CSGO_APP_ID, MSG_TYPES.OPEN_CRATE, null, encodedPayload);

        log(`开箱请求已发送`, 'SUCCESS');

        // 等待结果
        const result = await waitForCrateResult(crateKey, options.timeout || config.timeout);

        return result;

    } catch (err) {
        log(`开箱失败: ${err.message}`, 'ERROR');
        openingCrates.delete(crateKey);
        return { success: false, error: err.message };
    }
}

// 等待开箱结果
function waitForCrateResult(crateKey, timeout = 30000) {
    return new Promise((resolve) => {
        const startTime = Date.now();

        const checkResult = () => {
            const crateInfo = openingCrates.get(crateKey);

            if (!crateInfo) {
                resolve({ success: false, error: '开箱记录丢失' });
                return;
            }

            // 检查是否有掉落物品
            if (crateInfo.droppedItems.length > 0) {
                log(`开箱成功！获得 ${crateInfo.droppedItems.length} 个物品`, 'SUCCESS');
                openingCrates.delete(crateKey);
                resolve({
                    success: true,
                    items: crateInfo.droppedItems,
                    duration: Date.now() - crateInfo.timestamp
                });
                return;
            }

            // 检查超时
            if (Date.now() - startTime > timeout) {
                log('开箱超时', 'ERROR');
                openingCrates.delete(crateKey);
                resolve({ success: false, error: '开箱超时' });
                return;
            }

            // 继续等待
            setTimeout(checkResult, 1000);
        };

        checkResult();
    });
}

// 批量开箱方法
async function unlockMultipleCrates(crateList, options = {}) {
    const results = [];
    const batchDelay = options.batchDelay || 2000; // 每次开箱间隔2秒

    log(`开始批量开箱，共 ${crateList.length} 个箱子`, 'CRATE');

    for (let i = 0; i < crateList.length; i++) {
        const { keyId, caseId } = crateList[i];

        log(`批量开箱进度: ${i + 1}/${crateList.length}`, 'INFO');

        const result = await unlockCrate(keyId, caseId, options);
        results.push({
            keyId,
            caseId,
            ...result
        });

        // 如果不是最后一个，等待一段时间
        if (i < crateList.length - 1) {
            await delay(batchDelay);
        }
    }

    // 统计结果
    const successful = results.filter(r => r.success).length;
    const failed = results.length - successful;

    log(`批量开箱完成 - 成功: ${successful}, 失败: ${failed}`, 'SUCCESS');

    return results;
}

// 获取库存信息（需要额外实现）
function getInventory() {
    // 这里可以实现获取库存的逻辑
    // 需要额外的 proto 消息定义
    log('获取库存功能需要额外实现', 'WARNING');
}

// 清理过期的开箱记录
function cleanupExpiredCrates() {
    const now = Date.now();
    const expiredTime = 5 * 60 * 1000; // 5分钟

    for (let [key, info] of openingCrates) {
        if (now - info.timestamp > expiredTime) {
            log(`清理过期开箱记录: ${key}`, 'INFO');
            openingCrates.delete(key);
        }
    }
}

// 定期清理过期记录
setInterval(cleanupExpiredCrates, 60000); // 每分钟清理一次

// 优雅关闭
process.on('SIGINT', () => {
    log('正在关闭程序...', 'INFO');
    client.logOff();
    process.exit(0);
});

// 示例使用
async function main() {
    // 等待连接到 GC
    log('等待连接到游戏协调器...', 'INFO');

    // 单个开箱示例
    // const result = await unlockCrate(44104035671, 44615519345);
    // log(`开箱结果: ${JSON.stringify(result)}`, 'INFO');

    // 批量开箱示例
    // const crateList = [
    //     { keyId: 44104035671, caseId: 44615519345 },
    //     { keyId: 44104035672, caseId: 44615519346 }
    // ];
    // const results = await unlockMultipleCrates(crateList);
    // log(`批量开箱结果: ${JSON.stringify(results)}`, 'INFO');
}

// 如果直接运行此文件，执行主函数
if (require.main === module) {
    main().catch(err => {
        log(`程序错误: ${err.message}`, 'ERROR');
    });
}