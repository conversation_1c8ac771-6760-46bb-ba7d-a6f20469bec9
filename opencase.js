const SteamUser = require('steam-user');
const SteamID = require('steamid');
const protobuf = require('protobufjs');
const fs = require('fs');
const path = require("path");

// 你的 Steam 账号信息
const client = new SteamUser();

// CSGO AppID
const CSGO_APP_ID = 730;

// proto 文件
const root = protobuf.loadSync(
    path.join(__dirname, "proto", "econ_gcmessages.proto")
);
const ItemCustomization = root.lookupType('CMsgGCItemCustomizationNotification');

client.logOn({
    accountName: 'an666459',
    password: '8imUXpPByQXs',
    twoFactorCode: require('steam-totp').getAuthCode('HsoQxtAzpSGsTVVXrsxku0+398M=')
});

client.on('loggedOn', () => {
    console.log('✅ 已登录 Steam');

    // 告知 Steam 在玩 CSGO
    client.gamesPlayed([CSGO_APP_ID]);
});

client.on('playingState', (blocked, apps) => {
    console.log('正在玩游戏状态:', apps);
});

// 监听 GC 消息
client.on('gcMessage', (appid, msgType, proto) => {
    if (appid !== CSGO_APP_ID) return;

    console.log('消息类型:', msgType);
    // 开箱掉落通知
    if (msgType === 1090) {
        try {
            const decoded = ItemCustomization.decode(proto);

            if (decoded.item_id) {
                decoded.item_id.forEach(id => {
                    console.log(`开箱掉落物品资产ID: ${id}`);
                });
            }

            if (decoded.extra_data) {
                decoded.extra_data.forEach(ex => {
                    console.log(`开箱掉落物品: ${ex}`);
                });
            }

            console.log('新物品消息:', decoded.request);
        } catch (err) {
            console.log('解析1090消息失败:', err.message);
        }
    }
});

// 开箱方法示例
function unlockCrate(keyId, caseId) {

    const payload ={
        tool_item_id: keyId,
        subject_item_id: caseId,
        for_rental: false
    };

    console.log(0)
    // 发送 GC 消息
    client.sendToGC(CSGO_APP_ID, 2534, null,payload); // 2534 对应 k_EMsgGCOpenCrate

    console.log(1)
}

// 测试开箱（这里需要你的 keyId 和 caseId）
unlockCrate(44104035671, 44615519345);