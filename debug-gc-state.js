// 调试 GC 连接状态的脚本
const SteamUser = require('steam-user');
const protobuf = require('protobufjs');
const path = require('path');

// 加载配置
let config;
try {
    config = require('./config.js');
} catch (err) {
    console.error('❌ 配置文件不存在');
    process.exit(1);
}

const client = new SteamUser();
const CSGO_APP_ID = 730;

function log(msg, type = 'INFO') {
    const time = new Date().toISOString();
    const emoji = { 
        INFO: '📝', 
        SUCCESS: '✅', 
        ERROR: '❌', 
        WARNING: '⚠️',
        DEBUG: '🔍',
        CRATE: '📦'
    }[type] || '📝';
    console.log(`[${time}] ${emoji} ${msg}`);
}

// 连接状态跟踪
let connectionState = {
    steamLoggedIn: false,
    gameStarted: false,
    gcConnected: false,
    clientWelcomeReceived: false,
    messagesReceived: [],
    connectionEvents: []
};

// 记录连接事件
function recordEvent(event, details = '') {
    const timestamp = Date.now();
    connectionState.connectionEvents.push({
        timestamp,
        event,
        details,
        time: new Date(timestamp).toISOString()
    });
    log(`连接事件: ${event} ${details}`, 'DEBUG');
}

// 加载 proto
let ClientHelloType, OpenCrateType;
try {
    const gcRoot = protobuf.loadSync(path.join(__dirname, "proto", "gc_messages.proto"));
    ClientHelloType = gcRoot.lookupType('CMsgClientHello');
    
    const crateRoot = protobuf.loadSync(path.join(__dirname, "proto", "opencrate.proto"));
    OpenCrateType = crateRoot.lookupType('CMsgOpenCrate');
    
    log('Proto 文件加载成功', 'SUCCESS');
} catch (err) {
    log(`Proto 文件加载失败: ${err.message}`, 'ERROR');
    process.exit(1);
}

// 创建 ClientHello
function createClientHelloMessage() {
    const helloData = {
        version: 2000202,
        socache_have_versions: [],
        client_session_need: 1,
        client_launcher: 0,
        steam_launcher: 1
    };
    return ClientHelloType.encode(helloData).finish();
}

// 发送 GC 消息
function sendGCMessage(msgType, payload, name = '') {
    try {
        client.sendToGC(CSGO_APP_ID, msgType, {}, payload);
        recordEvent('GC_MESSAGE_SENT', `${name || msgType}`);
        log(`${name || msgType} 消息已发送`, 'SUCCESS');
        return true;
    } catch (err) {
        recordEvent('GC_MESSAGE_SEND_FAILED', `${name || msgType}: ${err.message}`);
        log(`发送 ${name || msgType} 失败: ${err.message}`, 'ERROR');
        return false;
    }
}

// 显示连接状态
function showConnectionState() {
    log('=== 当前连接状态 ===', 'INFO');
    log(`Steam 登录: ${connectionState.steamLoggedIn}`, 'INFO');
    log(`游戏启动: ${connectionState.gameStarted}`, 'INFO');
    log(`GC 连接: ${connectionState.gcConnected}`, 'INFO');
    log(`ClientWelcome: ${connectionState.clientWelcomeReceived}`, 'INFO');
    log(`收到消息数: ${connectionState.messagesReceived.length}`, 'INFO');
    
    if (connectionState.messagesReceived.length > 0) {
        log(`消息类型: ${connectionState.messagesReceived.join(', ')}`, 'INFO');
    }
    
    log('=== 连接事件历史 ===', 'INFO');
    connectionState.connectionEvents.forEach((event, index) => {
        log(`${index + 1}. [${event.time}] ${event.event} ${event.details}`, 'INFO');
    });
}

// Steam 事件处理
client.on('loggedOn', () => {
    connectionState.steamLoggedIn = true;
    recordEvent('STEAM_LOGGED_IN', client.steamID.toString());
    log('Steam 登录成功', 'SUCCESS');
    log(`Steam ID: ${client.steamID}`, 'INFO');
    client.gamesPlayed([CSGO_APP_ID]);
});

client.on('appLaunched', (appid) => {
    if (appid === CSGO_APP_ID) {
        connectionState.gameStarted = true;
        recordEvent('GAME_STARTED', 'CSGO');
        log('CSGO 启动成功', 'SUCCESS');
        
        setTimeout(() => {
            log('发送 ClientHello', 'INFO');
            const helloMessage = createClientHelloMessage();
            sendGCMessage(4006, helloMessage, 'ClientHello');
        }, 3000);
    }
});

// 注意：steam-user 库没有 gcConnected 事件！
// 我们通过收到 ClientWelcome 消息来判断 GC 连接成功

client.on('gcDisconnected', (appid) => {
    if (appid === CSGO_APP_ID) {
        connectionState.gcConnected = false;
        recordEvent('GC_DISCONNECTED', 'CSGO');
        log('GC 连接断开', 'WARNING');
    }
});

client.on('receivedFromGC', (appid, msgType, payload) => {
    if (appid === CSGO_APP_ID) {
        connectionState.messagesReceived.push(msgType);
        recordEvent('GC_MESSAGE_RECEIVED', `${msgType} (${payload.length} bytes)`);
        log(`收到 GC 消息: ${msgType} (${payload.length} 字节)`, 'SUCCESS');
        
        switch (msgType) {
            case 4004: // ClientWelcome
                connectionState.clientWelcomeReceived = true;
                connectionState.gcConnected = true; // ClientWelcome 表示 GC 连接成功
                recordEvent('CLIENT_WELCOME_RECEIVED', 'GC 连接成功');
                log('  -> ClientWelcome 消息', 'SUCCESS');
                log('🎉 GC 连接成功！(通过 ClientWelcome 确认)', 'SUCCESS');

                // 显示当前状态
                setTimeout(() => {
                    showConnectionState();
                    log('GC 已连接，可以开始开箱', 'SUCCESS');
                    setTimeout(() => testOpenCrate(), 2000);
                }, 1000);
                break;
                
            case 1090: // 物品掉落通知
                log('  -> 物品掉落通知 🎉', 'CRATE');
                break;
                
            case 1008: // 开箱响应
                log('  -> 开箱响应', 'CRATE');
                log('开箱可能失败，未收到物品掉落', 'WARNING');
                break;
                
            default:
                log(`  -> 消息类型: ${msgType}`, 'DEBUG');
                break;
        }
    }
});

// 测试开箱
async function testOpenCrate() {
    log('=== 开始开箱测试 ===', 'CRATE');
    
    // 检查所有必要条件
    if (!connectionState.steamLoggedIn) {
        log('Steam 未登录', 'ERROR');
        return;
    }
    
    if (!connectionState.gameStarted) {
        log('游戏未启动', 'ERROR');
        return;
    }
    
    if (!connectionState.gcConnected) {
        log('GC 未连接', 'ERROR');
        return;
    }
    
    if (!connectionState.clientWelcomeReceived) {
        log('未收到 ClientWelcome', 'ERROR');
        return;
    }
    
    log('所有连接条件满足，发送开箱请求', 'SUCCESS');
    
    const keyId = 44104035671;
    const caseId = 44615519345;
    
    const openCrateData = {
        tool_item_id: keyId,
        subject_item_id: caseId,
        for_rental: false
    };
    
    const encoded = OpenCrateType.encode(openCrateData).finish();
    log(`开箱消息编码成功 (${encoded.length} 字节)`, 'DEBUG');
    
    const sent = sendGCMessage(2534, encoded, '开箱请求');
    
    if (sent) {
        log('开箱请求已发送，等待响应...', 'SUCCESS');
        
        // 30秒后检查结果
        setTimeout(() => {
            const recentMessages = connectionState.messagesReceived.slice(-5);
            log(`最近收到的消息: ${recentMessages.join(', ')}`, 'INFO');
            
            if (recentMessages.includes(1090)) {
                log('开箱成功！收到物品掉落消息', 'SUCCESS');
            } else if (recentMessages.includes(1008)) {
                log('开箱可能失败，只收到响应消息', 'WARNING');
            } else {
                log('未收到开箱相关响应', 'ERROR');
            }
            
            showConnectionState();
        }, 30000);
    }
}

client.on('error', (err) => {
    recordEvent('STEAM_ERROR', err.message);
    log(`Steam 错误: ${err.message}`, 'ERROR');
});

// 主函数
async function main() {
    log('=== GC 连接状态调试程序 ===', 'INFO');
    
    client.logOn({
        accountName: config.steam.accountName,
        password: config.steam.password,
        twoFactorCode: require('steam-totp').getAuthCode(config.steam.sharedSecret)
    });
    
    // 定期显示状态
    setInterval(() => {
        if (connectionState.messagesReceived.length > 0) {
            log(`状态检查 - GC连接: ${connectionState.gcConnected}, 消息数: ${connectionState.messagesReceived.length}`, 'DEBUG');
        }
    }, 30000);
    
    // 总超时
    setTimeout(() => {
        log('程序运行超时，显示最终状态', 'WARNING');
        showConnectionState();
        process.exit(0);
    }, 300000); // 5分钟
}

// 优雅退出
process.on('SIGINT', () => {
    log('程序被中断', 'WARNING');
    showConnectionState();
    client.logOff();
    process.exit(0);
});

if (require.main === module) {
    main().catch(err => {
        console.error('程序错误:', err);
        process.exit(1);
    });
}
