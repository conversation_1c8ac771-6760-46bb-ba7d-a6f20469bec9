const SteamUser = require('steam-user');
const SteamTotp = require('steam-totp');

const client = new SteamUser();

const account = {
    accountName: 'ja422373',
    password: 'rAGMZtTKZ3xg',
    shared_secret: 'pbAbhJYebURFyHhqOYWmNIkBVb4='
};

SteamTotp.getAuthCode(account.shared_secret, (err, code) => {
    if (err) {
        console.error('Code generation failed');
        return;
    }

    client.logOn({
        accountName: account.accountName,
        password: account.password,
        twoFactorCode: code // 自动生成的验证码
    });
});


// 3. 登录成功
client.on('loggedOn', (details) => {
    console.log('✅ 登录成功:', details);
});

// 4. 获取 refreshToken
client.on('loginKey', (refreshToken) => {
    console.log('🔑 refreshToken:', refreshToken);
    // 你可以保存它，未来用来无验证码登录
});

// 5. 获取 Web 会话信息（cookies）
client.on('webSession', (sessionID, cookies) => {
    console.log('🍪 Cookies:', cookies);
    console.log('🆔 WebSessionID:', sessionID);
});