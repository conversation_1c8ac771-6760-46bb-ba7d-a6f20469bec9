// 游戏协调器连接管理器
const EventEmitter = require('events');
const protobuf = require('protobufjs');
const path = require('path');

class GCConnectionManager extends EventEmitter {
    constructor(client, logger, appId = 730) {
        super();
        this.client = client;
        this.logger = logger;
        this.appId = appId;
        this.isConnected = false;
        this.connectionAttempts = 0;
        this.maxRetries = 5;
        this.retryDelay = 5000;
        this.connectionTimeout = 60000;

        // 加载 GC 消息 proto
        try {
            this.gcRoot = protobuf.loadSync(path.join(__dirname, "proto", "gc_messages.proto"));
            this.ClientHello = this.gcRoot.lookupType('CMsgClientHello');
            this.logger.log('GC 消息 proto 加载成功', 'DEBUG');
        } catch (err) {
            this.logger.log(`加载 GC 消息 proto 失败: ${err.message}`, 'ERROR');
            // 如果加载失败，使用简单的消息格式
            this.ClientHello = null;
        }

        this.setupEventListeners();
    }
    
    setupEventListeners() {
        // 监听 Steam 登录
        this.client.on('loggedOn', () => {
            this.logger.log('Steam 登录成功，启动游戏...', 'SUCCESS');
            this.client.gamesPlayed([this.appId]);
        });
        
        // 监听应用启动
        this.client.on('appLaunched', (appid) => {
            if (appid === this.appId) {
                this.logger.log('游戏应用已启动', 'SUCCESS');
                this.requestGCConnection();
            }
        });
        
        // 监听 GC 连接
        this.client.on('gcConnected', (appid) => {
            if (appid === this.appId) {
                this.isConnected = true;
                this.connectionAttempts = 0;
                this.logger.log('游戏协调器连接成功', 'SUCCESS');
                this.emit('connected');
            }
        });
        
        // 监听 GC 断开
        this.client.on('gcDisconnected', (appid) => {
            if (appid === this.appId) {
                this.isConnected = false;
                this.logger.log('游戏协调器连接断开', 'WARNING');
                this.emit('disconnected');
                
                // 自动重连
                this.scheduleReconnect();
            }
        });
        
        // 监听 GC 消息（用于检测连接活跃度）
        this.client.on('gcMessage', (appid, msgType, proto) => {
            if (appid === this.appId) {
                this.emit('message', msgType, proto);
            }
        });
    }
    
    // 请求 GC 连接
    requestGCConnection() {
        if (this.isConnected) {
            this.logger.log('游戏协调器已连接', 'INFO');
            return;
        }

        this.connectionAttempts++;
        this.logger.log(`尝试连接游戏协调器 (第 ${this.connectionAttempts} 次)`, 'INFO');

        try {
            // 构建 ClientHello 消息
            let helloPayload;

            if (this.ClientHello) {
                // 使用正确的 ClientHello 消息格式
                const helloMessage = {
                    version: 18, // CSGO 客户端版本
                    client_session_need: 1,
                    client_launcher: 0,
                    steam_launcher: 1
                };

                helloPayload = this.ClientHello.encode(helloMessage).finish();
                this.logger.log('发送结构化 ClientHello 消息', 'DEBUG');
            } else {
                // 回退到简单格式
                helloPayload = Buffer.alloc(0);
                this.logger.log('发送简单 ClientHello 消息', 'DEBUG');
            }

            // 发送 ClientHello 消息 (消息类型 4006)
            this.client.sendToGC(this.appId, 4006, null, helloPayload);

            // 延迟发送其他消息来确保连接
            setTimeout(() => {
                if (!this.isConnected) {
                    this.logger.log('发送额外的连接消息', 'DEBUG');

                    // 尝试发送 ClientWelcome 请求 (消息类型 4004)
                    this.client.sendToGC(this.appId, 4004, null, Buffer.alloc(0));

                    // 也尝试发送连接状态消息 (消息类型 4001)
                    setTimeout(() => {
                        if (!this.isConnected) {
                            this.client.sendToGC(this.appId, 4001, null, Buffer.alloc(0));
                        }
                    }, 1000);
                }
            }, 2000);

        } catch (err) {
            this.logger.log(`发送 GC 连接请求失败: ${err.message}`, 'ERROR');
        }
    }
    
    // 计划重连
    scheduleReconnect() {
        if (this.connectionAttempts >= this.maxRetries) {
            this.logger.log('达到最大重连次数，停止重连', 'ERROR');
            this.emit('maxRetriesReached');
            return;
        }
        
        const delay = this.retryDelay * Math.pow(2, this.connectionAttempts - 1); // 指数退避
        this.logger.log(`${delay/1000} 秒后尝试重连...`, 'INFO');
        
        setTimeout(() => {
            this.requestGCConnection();
        }, delay);
    }
    
    // 等待连接
    async waitForConnection(timeout = this.connectionTimeout) {
        if (this.isConnected) {
            return true;
        }
        
        return new Promise((resolve, reject) => {
            const timeoutId = setTimeout(() => {
                this.removeAllListeners('connected');
                this.removeAllListeners('maxRetriesReached');
                reject(new Error('等待游戏协调器连接超时'));
            }, timeout);
            
            const onConnected = () => {
                clearTimeout(timeoutId);
                this.removeAllListeners('maxRetriesReached');
                resolve(true);
            };
            
            const onMaxRetries = () => {
                clearTimeout(timeoutId);
                this.removeAllListeners('connected');
                reject(new Error('达到最大重连次数'));
            };
            
            this.once('connected', onConnected);
            this.once('maxRetriesReached', onMaxRetries);
            
            // 如果还没开始连接，主动请求连接
            if (this.connectionAttempts === 0) {
                this.requestGCConnection();
            }
        });
    }
    
    // 强制重连
    forceReconnect() {
        this.logger.log('强制重新连接游戏协调器', 'INFO');
        this.connectionAttempts = 0;
        this.requestGCConnection();
    }
    
    // 检查连接状态
    checkConnection() {
        return this.isConnected;
    }
    
    // 发送 GC 消息的包装方法
    sendToGC(msgType, protobuf, payload) {
        if (!this.isConnected) {
            throw new Error('游戏协调器未连接');
        }
        
        try {
            this.client.sendToGC(this.appId, msgType, protobuf, payload);
            return true;
        } catch (err) {
            this.logger.log(`发送 GC 消息失败: ${err.message}`, 'ERROR');
            return false;
        }
    }
    
    // 获取连接统计信息
    getStats() {
        return {
            isConnected: this.isConnected,
            connectionAttempts: this.connectionAttempts,
            maxRetries: this.maxRetries
        };
    }
}

module.exports = GCConnectionManager;
