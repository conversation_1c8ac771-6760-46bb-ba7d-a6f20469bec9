// 修正版开箱测试 - 基于 C# 代码结构
const SteamUser = require('steam-user');
const protobuf = require('protobufjs');
const path = require('path');

// 加载配置
let config;
try {
    config = require('./config.js');
} catch (err) {
    console.error('❌ 配置文件不存在');
    process.exit(1);
}

const client = new SteamUser();
const CSGO_APP_ID = 730;

// 消息类型常量 (对应 C# 中的枚举)
const EGCItemMsg = {
    k_EMsgGCOpenCrate: 2534  // 对应 C# 中的 EGCItemMsg.k_EMsgGCOpenCrate
};

const EGCBaseClientMsg = {
    k_EMsgGCClientHello: 4006
};

function log(msg, type = 'INFO') {
    const time = new Date().toISOString();
    const emoji = { 
        INFO: '📝', 
        SUCCESS: '✅', 
        ERROR: '❌', 
        WARNING: '⚠️',
        DEBUG: '🔍',
        CRATE: '📦'
    }[type] || '📝';
    console.log(`[${time}] ${emoji} ${msg}`);
}

let gcConnected = false;
let messagesReceived = [];
let openingCrates = new Map();

// 加载 proto 文件
let ClientHelloType, OpenCrateType, ItemCustomizationType;
try {
    const gcRoot = protobuf.loadSync(path.join(__dirname, "proto", "gc_messages.proto"));
    ClientHelloType = gcRoot.lookupType('CMsgClientHello');
    
    const crateRoot = protobuf.loadSync(path.join(__dirname, "proto", "opencrate.proto"));
    OpenCrateType = crateRoot.lookupType('CMsgOpenCrate');
    
    const econRoot = protobuf.loadSync(path.join(__dirname, "proto", "econ_gcmessages.proto"));
    ItemCustomizationType = econRoot.lookupType('CMsgGCItemCustomizationNotification');
    
    log('所有 Proto 文件加载成功', 'SUCCESS');
} catch (err) {
    log(`Proto 文件加载失败: ${err.message}`, 'ERROR');
    process.exit(1);
}

// 创建 ClientHello 消息
function createClientHelloMessage() {
    const helloData = {
        version: 2000202,
        socache_have_versions: [],
        client_session_need: 1,
        client_launcher: 0,
        steam_launcher: 1
    };
    
    return ClientHelloType.encode(helloData).finish();
}

// 发送 GC 消息
function sendGCMessage(msgType, payload, name = '') {
    try {
        client.sendToGC(CSGO_APP_ID, msgType, {}, payload);
        log(`${name || msgType} 消息已发送`, 'SUCCESS');
        return true;
    } catch (err) {
        log(`发送 ${name || msgType} 失败: ${err.message}`, 'ERROR');
        return false;
    }
}

// 开箱函数 - 完全按照 C# 代码逻辑
async function openCrate(keyId, caseId) {
    const crateKey = `${keyId}_${caseId}`;
    
    if (!gcConnected && messagesReceived.length === 0) {
        log('GC 未连接，无法开箱', 'ERROR');
        return { success: false, error: 'GC 未连接' };
    }
    
    try {
        log(`开始开箱 - 钥匙: ${keyId}, 箱子: ${caseId}`, 'CRATE');
        
        // 创建开箱记录
        const crateInfo = {
            keyId,
            caseId,
            timestamp: Date.now(),
            droppedItems: []
        };
        
        openingCrates.set(crateKey, crateInfo);
        
        // 构建开箱消息 - 对应 C# 代码
        // var unlockCrateMsg1 = new ClientGCMsgProtobuf<CMsgOpenCrate>((uint) EGCItemMsg.k_EMsgGCOpenCrate);
        // unlockCrateMsg1.Body.tool_item_id = keyId;
        // unlockCrateMsg1.Body.subject_item_id = caseId;
        // unlockCrateMsg1.Body.for_rental = false;
        
        const openCrateData = {
            tool_item_id: keyId,      // 对应 C# ulong keyId
            subject_item_id: caseId,  // 对应 C# ulong caseId
            for_rental: false         // 对应 C# bool false
        };
        
        log(`发送: crate_item_id=${caseId}, key_item_id=${keyId}`, 'INFO');
        
        const encoded = OpenCrateType.encode(openCrateData).finish();
        log(`开箱消息编码成功 (${encoded.length} 字节)`, 'DEBUG');
        
        // 发送开箱请求 - 对应 C# gc?.Send(unlockCrateMsg1, CSGO_APP_ID);
        const sent = sendGCMessage(EGCItemMsg.k_EMsgGCOpenCrate, encoded, '开箱请求');
        
        if (!sent) {
            openingCrates.delete(crateKey);
            return { success: false, error: '发送开箱请求失败' };
        }
        
        log('开箱请求已发送，等待响应...', 'SUCCESS');
        
        // 等待开箱结果
        const result = await waitForCrateResult(crateKey, 45000); // 45秒超时
        return result;
        
    } catch (err) {
        log(`开箱失败: ${err.message}`, 'ERROR');
        openingCrates.delete(crateKey);
        return { success: false, error: err.message };
    }
}

// 等待开箱结果
function waitForCrateResult(crateKey, timeout = 45000) {
    return new Promise((resolve) => {
        const startTime = Date.now();
        
        const checkResult = () => {
            const crateInfo = openingCrates.get(crateKey);
            
            if (!crateInfo) {
                resolve({ success: false, error: '开箱记录丢失' });
                return;
            }
            
            // 检查是否有掉落物品
            if (crateInfo.droppedItems.length > 0) {
                log(`开箱成功！获得 ${crateInfo.droppedItems.length} 个物品`, 'SUCCESS');
                openingCrates.delete(crateKey);
                resolve({ 
                    success: true, 
                    items: crateInfo.droppedItems,
                    duration: Date.now() - crateInfo.timestamp
                });
                return;
            }
            
            // 检查超时
            if (Date.now() - startTime > timeout) {
                log('开箱超时', 'ERROR');
                openingCrates.delete(crateKey);
                resolve({ success: false, error: '开箱超时' });
                return;
            }
            
            // 继续等待
            setTimeout(checkResult, 1000);
        };
        
        checkResult();
    });
}

// Steam 事件处理
client.on('loggedOn', () => {
    log('Steam 登录成功', 'SUCCESS');
    log(`Steam ID: ${client.steamID}`, 'INFO');
    client.gamesPlayed([CSGO_APP_ID]);
});

client.on('appLaunched', (appid) => {
    if (appid === CSGO_APP_ID) {
        log('CSGO 启动成功', 'SUCCESS');
        
        setTimeout(() => {
            log('发送 ClientHello', 'INFO');
            const helloMessage = createClientHelloMessage();
            sendGCMessage(EGCBaseClientMsg.k_EMsgGCClientHello, helloMessage, 'ClientHello');
        }, 3000);
    }
});

client.on('gcConnected', (appid) => {
    if (appid === CSGO_APP_ID) {
        gcConnected = true;
        log('🎉 GC 连接成功！', 'SUCCESS');
    }
});

client.on('receivedFromGC', (appid, msgType, payload) => {
    if (appid === CSGO_APP_ID) {
        messagesReceived.push(msgType);
        log(`收到 GC 消息: ${msgType} (${payload.length} 字节)`, 'SUCCESS');
        
        switch (msgType) {
            case 4004: // ClientWelcome
                log('  -> ClientWelcome 消息', 'SUCCESS');
                log('GC 连接已建立，可以开始开箱', 'SUCCESS');
                
                // 自动开始测试开箱
                setTimeout(() => {
                    testOpenCrate();
                }, 3000);
                break;
                
            case 1090: // 物品掉落通知
                log('  -> 物品掉落通知', 'CRATE');
                handleItemDrop(payload);
                break;
                
            case 1008: // 开箱响应
                log('  -> 开箱响应', 'CRATE');
                break;
                
            default:
                log(`  -> 消息类型: ${msgType}`, 'DEBUG');
                break;
        }
    }
});

// 处理物品掉落
function handleItemDrop(payload) {
    try {
        const decoded = ItemCustomizationType.decode(payload);
        
        if (decoded.item_id && decoded.item_id.length > 0) {
            decoded.item_id.forEach((id, index) => {
                log(`掉落物品 ${index + 1}: ${id}`, 'SUCCESS');
                
                // 检查是否是我们正在跟踪的开箱
                for (let [crateKey, crateInfo] of openingCrates) {
                    if (Date.now() - crateInfo.timestamp < 60000) {
                        crateInfo.droppedItems.push(id.toString());
                        log(`开箱 ${crateKey} 获得物品: ${id}`, 'CRATE');
                    }
                }
            });
        }
        
    } catch (err) {
        log(`解析物品掉落失败: ${err.message}`, 'ERROR');
    }
}

// 测试开箱
async function testOpenCrate() {
    log('=== 开始测试开箱 ===', 'CRATE');
    
    // 使用您提供的 ID（请替换为实际的 ID）
    const keyId = 44104035671;
    const caseId = 44615519345;
    
    log(`测试参数: keyId=${keyId}, caseId=${caseId}`, 'INFO');
    
    const result = await openCrate(keyId, caseId);
    
    if (result.success) {
        log('🎉 开箱成功！', 'SUCCESS');
        log(`获得物品: ${result.items.join(', ')}`, 'CRATE');
        log(`耗时: ${result.duration}ms`, 'INFO');
    } else {
        log(`开箱失败: ${result.error}`, 'ERROR');
    }
    
    log('=== 开箱测试完成 ===', 'INFO');
}

client.on('error', (err) => {
    log(`Steam 错误: ${err.message}`, 'ERROR');
});

// 主函数
async function main() {
    log('=== 修正版开箱测试程序 ===', 'INFO');
    log('基于 C# 代码结构和正确的 proto 定义', 'INFO');
    
    client.logOn({
        accountName: config.steam.accountName,
        password: config.steam.password,
        twoFactorCode: require('steam-totp').getAuthCode(config.steam.sharedSecret)
    });
    
    // 总超时
    setTimeout(() => {
        if (messagesReceived.length === 0) {
            log('未收到任何 GC 消息，测试失败', 'ERROR');
            process.exit(1);
        }
    }, 120000); // 2分钟总超时
}

// 优雅退出
process.on('SIGINT', () => {
    log('程序被中断', 'WARNING');
    log(`总共收到 ${messagesReceived.length} 个 GC 消息`, 'INFO');
    if (openingCrates.size > 0) {
        log(`还有 ${openingCrates.size} 个开箱正在进行`, 'WARNING');
    }
    client.logOff();
    process.exit(0);
});

if (require.main === module) {
    main().catch(err => {
        console.error('程序错误:', err);
        process.exit(1);
    });
}

module.exports = { openCrate, log };
