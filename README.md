# CSGO 开箱工具

一个功能完善的 CSGO 开箱自动化工具，支持单个开箱和批量开箱。

## 功能特性

- ✅ 安全的 Steam 登录和 GC 连接
- 📦 单个开箱和批量开箱支持
- 🔄 自动重试机制
- 📝 详细的日志记录
- ⚙️ 灵活的配置选项
- 🛡️ 错误处理和超时保护
- 📊 开箱结果统计

## 安装依赖

```bash
npm install
```

## 配置设置

1. 复制配置文件模板：
```bash
cp config.example.js config.js
```

2. 编辑 `config.js` 文件，填入你的 Steam 账号信息：
```javascript
module.exports = {
    steam: {
        accountName: 'your_steam_username',
        password: 'your_steam_password',
        sharedSecret: 'your_shared_secret_for_2fa',
    },
    // ... 其他配置
};
```

## 使用方法

### 方法1: 使用安全版本（推荐）

```javascript
const { unlockCrate, unlockMultipleCrates } = require('./opencase-secure');

// 单个开箱
const result = await unlockCrate(keyId, caseId);

// 批量开箱
const crateList = [
    { keyId: 123456, caseId: 789012 },
    { keyId: 123457, caseId: 789013 }
];
const results = await unlockMultipleCrates(crateList);
```

### 方法2: 运行示例

```bash
node example-usage.js
```

### 方法3: 直接运行原始版本

```bash
node opencase.js
```

## API 文档

### unlockCrate(keyId, caseId, options)

开启单个箱子。

**参数:**
- `keyId` (number): 钥匙的资产ID
- `caseId` (number): 箱子的资产ID  
- `options` (object, 可选): 配置选项
  - `forRental` (boolean): 是否为租赁物品
  - `timeout` (number): 超时时间（毫秒）

**返回值:**
```javascript
{
    success: boolean,
    items?: string[],      // 获得的物品ID列表
    duration?: number,     // 开箱耗时
    error?: string         // 错误信息
}
```

### unlockMultipleCrates(crateList, options)

批量开箱。

**参数:**
- `crateList` (array): 箱子列表
  ```javascript
  [
      { keyId: number, caseId: number },
      // ...
  ]
  ```
- `options` (object, 可选): 配置选项
  - `batchDelay` (number): 批量开箱间隔时间
  - `timeout` (number): 每个箱子的超时时间

**返回值:**
```javascript
[
    {
        keyId: number,
        caseId: number,
        success: boolean,
        items?: string[],
        duration?: number,
        error?: string
    },
    // ...
]
```

## 配置选项

### Steam 配置
- `accountName`: Steam 用户名
- `password`: Steam 密码
- `sharedSecret`: 两步验证密钥

### 开箱配置
- `maxRetries`: 最大重试次数
- `retryDelay`: 重试延迟时间
- `timeout`: 开箱超时时间
- `batchDelay`: 批量开箱间隔时间

### 日志配置
- `level`: 日志级别 (DEBUG, INFO, WARNING, ERROR)
- `saveToFile`: 是否保存到文件
- `logFile`: 日志文件名

## 获取物品ID

你需要获取钥匙和箱子的资产ID才能使用此工具。可以通过以下方式获取：

1. 使用 Steam API
2. 使用浏览器开发者工具查看库存
3. 使用第三方工具

## 注意事项

⚠️ **重要提醒:**

1. **账号安全**: 请妥善保管你的 Steam 账号信息，不要分享给他人
2. **使用风险**: 使用自动化工具可能违反 Steam 服务条款，请自行承担风险
3. **频率限制**: 不要过于频繁地开箱，可能会被 Steam 限制
4. **备份配置**: 请备份你的配置文件，避免意外丢失

## 故障排除

### 常见问题

1. **连接游戏协调器失败**
   - 检查网络连接
   - 确认 CSGO 服务器状态
   - 重启程序

2. **开箱超时**
   - 增加超时时间配置
   - 检查网络延迟
   - 减少并发开箱数量

3. **登录失败**
   - 检查账号密码
   - 确认两步验证设置
   - 检查 Steam Guard

## 文件结构

```
├── opencase.js           # 原始开箱代码（已完善）
├── opencase-secure.js    # 安全版本（推荐使用）
├── example-usage.js      # 使用示例
├── config.example.js     # 配置文件模板
├── config.js            # 实际配置文件（需要创建）
├── proto/               # Protocol Buffers 定义
│   ├── econ_gcmessages.proto
│   └── opencrate.proto
└── README.md            # 说明文档
```

## 更新日志

### v2.0.0
- 重构代码架构
- 添加配置文件支持
- 改进错误处理
- 添加批量开箱功能
- 完善日志系统
- 添加安全版本

### v1.0.0
- 基础开箱功能
- Steam 登录和 GC 连接
- 物品掉落监听

## 许可证

此项目仅供学习和研究使用。使用者需要自行承担使用风险。
