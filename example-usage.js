// 开箱使用示例
const { unlockCrate, unlockMultipleCrates, logger } = require('./opencase-secure');

// 等待连接到游戏协调器
function waitForConnection() {
    return new Promise((resolve) => {
        const checkConnection = () => {
            // 这里可以添加连接检查逻辑
            setTimeout(() => {
                logger.log('等待连接到游戏协调器...', 'INFO');
                resolve();
            }, 5000);
        };
        checkConnection();
    });
}

async function main() {
    try {
        // 等待连接
        await waitForConnection();
        
        // 示例1: 单个开箱
        logger.log('=== 单个开箱示例 ===', 'INFO');
        const singleResult = await unlockCrate(
            44104035671,  // 钥匙ID
            44615519345   // 箱子ID
        );
        
        if (singleResult.success) {
            logger.log(`开箱成功！获得物品: ${singleResult.items.join(', ')}`, 'SUCCESS');
            logger.log(`耗时: ${singleResult.duration}ms`, 'INFO');
        } else {
            logger.log(`开箱失败: ${singleResult.error}`, 'ERROR');
        }
        
        // 等待一段时间
        await new Promise(resolve => setTimeout(resolve, 3000));
        
        // // 示例2: 批量开箱
        // logger.log('=== 批量开箱示例 ===', 'INFO');
        // const crateList = [
        //     { keyId: 44104035671, caseId: 44615519345 },
        //     { keyId: 44104035672, caseId: 44615519346 },
        //     { keyId: 44104035673, caseId: 44615519347 }
        // ];
        //
        // const batchResults = await unlockMultipleCrates(crateList, {
        //     batchDelay: 3000,  // 每次开箱间隔3秒
        //     timeout: 45000     // 每个箱子45秒超时
        // });
        //
        // // 分析结果
        // const successfulCrates = batchResults.filter(r => r.success);
        // const failedCrates = batchResults.filter(r => !r.success);
        //
        // logger.log(`批量开箱结果统计:`, 'INFO');
        // logger.log(`- 成功: ${successfulCrates.length}`, 'SUCCESS');
        // logger.log(`- 失败: ${failedCrates.length}`, 'ERROR');
        
        // // 显示获得的所有物品
        // const allItems = successfulCrates.flatMap(r => r.items || []);
        // if (allItems.length > 0) {
        //     logger.log(`获得的所有物品ID: ${allItems.join(', ')}`, 'SUCCESS');
        // }
        //
        // // 显示失败的原因
        // failedCrates.forEach(failed => {
        //     logger.log(`开箱失败 (钥匙: ${failed.keyId}, 箱子: ${failed.caseId}): ${failed.error}`, 'ERROR');
        // });
        
    } catch (error) {
        logger.log(`程序执行错误: ${error.message}`, 'ERROR');
    }
}

// 如果直接运行此文件
if (require.main === module) {
    main().catch(err => {
        console.error('程序错误:', err);
        process.exit(1);
    });
}

module.exports = { main };
