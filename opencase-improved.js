const SteamUser = require('steam-user');
const protobuf = require('protobufjs');
const path = require("path");
const GCConnectionManager = require('./gc-connection-manager');

// 尝试加载配置文件
let config;
try {
    config = require('./config.js');
} catch (err) {
    console.error('❌ 配置文件 config.js 不存在，请复制 config.example.js 为 config.js 并填入你的信息');
    process.exit(1);
}

// Steam 客户端
const client = new SteamUser();

// CSGO AppID
const CSGO_APP_ID = config.app.csgoAppId;

// 消息类型常量
const MSG_TYPES = {
    OPEN_CRATE: 2534,
    UNLOCK_CRATE: 1007,
    UNLOCK_CRATE_RESPONSE: 1008,
    ITEM_CUSTOMIZATION: 1090,
    USE_ITEM_REQUEST: 1025,
    USE_ITEM_RESPONSE: 1026
};

// proto 文件
const root = protobuf.loadSync(
    path.join(__dirname, "proto", "econ_gcmessages.proto")
);

const openCrateRoot = protobuf.loadSync(
    path.join(__dirname, "proto", "opencrate.proto")
);

const ItemCustomization = root.lookupType('CMsgGCItemCustomizationNotification');
const OpenCrateMsg = openCrateRoot.lookupType('CMsgOpenCrate');

// 日志系统
class Logger {
    constructor(config) {
        this.level = config.logging.level || 'INFO';
        this.saveToFile = config.logging.saveToFile || false;
        this.logFile = config.logging.logFile || 'opencase.log';
        this.levels = { DEBUG: 0, INFO: 1, WARNING: 2, ERROR: 3 };
    }
    
    log(message, type = 'INFO') {
        if (this.levels[type] < this.levels[this.level]) return;
        
        const timestamp = new Date().toISOString();
        const prefix = {
            'DEBUG': '🔍',
            'INFO': '📝',
            'SUCCESS': '✅',
            'ERROR': '❌',
            'WARNING': '⚠️',
            'CRATE': '📦'
        }[type] || '📝';
        
        const logMessage = `[${timestamp}] ${prefix} ${message}`;
        console.log(logMessage);
        
        if (this.saveToFile) {
            const fs = require('fs');
            fs.appendFileSync(this.logFile, logMessage + '\n');
        }
    }
}

const logger = new Logger(config);

// 创建 GC 连接管理器
const gcManager = new GCConnectionManager(client, logger, CSGO_APP_ID);

// 开箱状态跟踪
let openingCrates = new Map();

// 工具函数
function delay(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
}

// 处理物品掉落通知
function handleItemCustomization(proto) {
    try {
        const decoded = ItemCustomization.decode(proto);
        
        logger.log('收到物品掉落通知', 'CRATE');
        
        if (decoded.item_id && decoded.item_id.length > 0) {
            decoded.item_id.forEach((id, index) => {
                logger.log(`掉落物品 ${index + 1} 资产ID: ${id}`, 'SUCCESS');
                
                // 检查是否是我们正在跟踪的开箱
                for (let [crateKey, crateInfo] of openingCrates) {
                    if (Date.now() - crateInfo.timestamp < 60000) {
                        crateInfo.droppedItems.push(id);
                        logger.log(`开箱 ${crateKey} 获得物品: ${id}`, 'CRATE');
                    }
                }
            });
        }

        if (decoded.extra_data && decoded.extra_data.length > 0) {
            decoded.extra_data.forEach((data, index) => {
                logger.log(`额外数据 ${index + 1}: ${data}`, 'DEBUG');
            });
        }
        
    } catch (err) {
        logger.log(`解析物品掉落消息失败: ${err.message}`, 'ERROR');
    }
}

// 监听 GC 消息
gcManager.on('message', (msgType, proto) => {
    logger.log(`收到 GC 消息，类型: ${msgType}`, 'DEBUG');
    
    switch (msgType) {
        case MSG_TYPES.ITEM_CUSTOMIZATION:
            handleItemCustomization(proto);
            break;
        case MSG_TYPES.UNLOCK_CRATE_RESPONSE:
            logger.log('收到开箱响应', 'CRATE');
            break;
        case MSG_TYPES.USE_ITEM_RESPONSE:
            logger.log('收到使用物品响应', 'DEBUG');
            break;
        default:
            if (msgType >= 1000 && msgType <= 3000) {
                logger.log(`未处理的 GC 消息类型: ${msgType}`, 'DEBUG');
            }
            break;
    }
});

// 开箱核心类
class ImprovedCrateOpener {
    constructor(gcManager, logger, config) {
        this.gcManager = gcManager;
        this.logger = logger;
        this.config = config;
        this.openingCrates = new Map();
    }
    
    async unlockCrate(keyId, caseId, options = {}) {
        const crateKey = `${keyId}_${caseId}`;
        
        // 确保连接到 GC
        if (!this.gcManager.checkConnection()) {
            this.logger.log('游戏协调器未连接，等待连接...', 'WARNING');
            
            try {
                await this.gcManager.waitForConnection(options.connectionTimeout || 60000);
            } catch (err) {
                this.logger.log(`连接游戏协调器失败: ${err.message}`, 'ERROR');
                return { success: false, error: err.message };
            }
        }

        try {
            const crateInfo = {
                keyId,
                caseId,
                timestamp: Date.now(),
                droppedItems: [],
                retries: 0
            };
            
            this.openingCrates.set(crateKey, crateInfo);
            openingCrates.set(crateKey, crateInfo); // 全局引用
            
            this.logger.log(`开始开箱 - 钥匙ID: ${keyId}, 箱子ID: ${caseId}`, 'CRATE');
            
            const payload = {
                tool_item_id: keyId,
                subject_item_id: caseId,
                for_rental: options.forRental || false
            };
            
            const encodedPayload = OpenCrateMsg.encode(payload).finish();
            
            // 使用 GC 管理器发送消息
            const sent = this.gcManager.sendToGC(MSG_TYPES.OPEN_CRATE, null, encodedPayload);
            
            if (!sent) {
                throw new Error('发送开箱请求失败');
            }
            
            this.logger.log(`开箱请求已发送`, 'SUCCESS');
            
            const result = await this.waitForCrateResult(crateKey, options.timeout || this.config.crate.timeout);
            return result;
            
        } catch (err) {
            this.logger.log(`开箱失败: ${err.message}`, 'ERROR');
            this.openingCrates.delete(crateKey);
            openingCrates.delete(crateKey);
            return { success: false, error: err.message };
        }
    }
    
    waitForCrateResult(crateKey, timeout = 30000) {
        return new Promise((resolve) => {
            const startTime = Date.now();
            
            const checkResult = () => {
                const crateInfo = this.openingCrates.get(crateKey);
                
                if (!crateInfo) {
                    resolve({ success: false, error: '开箱记录丢失' });
                    return;
                }
                
                if (crateInfo.droppedItems.length > 0) {
                    this.logger.log(`开箱成功！获得 ${crateInfo.droppedItems.length} 个物品`, 'SUCCESS');
                    this.openingCrates.delete(crateKey);
                    openingCrates.delete(crateKey);
                    resolve({ 
                        success: true, 
                        items: crateInfo.droppedItems,
                        duration: Date.now() - crateInfo.timestamp
                    });
                    return;
                }
                
                if (Date.now() - startTime > timeout) {
                    this.logger.log('开箱超时', 'ERROR');
                    this.openingCrates.delete(crateKey);
                    openingCrates.delete(crateKey);
                    resolve({ success: false, error: '开箱超时' });
                    return;
                }
                
                setTimeout(checkResult, 1000);
            };
            
            checkResult();
        });
    }
    
    async unlockMultipleCrates(crateList, options = {}) {
        const results = [];
        const batchDelay = options.batchDelay || this.config.crate.batchDelay;
        
        this.logger.log(`开始批量开箱，共 ${crateList.length} 个箱子`, 'CRATE');
        
        for (let i = 0; i < crateList.length; i++) {
            const { keyId, caseId } = crateList[i];
            
            this.logger.log(`批量开箱进度: ${i + 1}/${crateList.length}`, 'INFO');
            
            const result = await this.unlockCrate(keyId, caseId, options);
            results.push({ keyId, caseId, ...result });
            
            if (i < crateList.length - 1) {
                await delay(batchDelay);
            }
        }
        
        const successful = results.filter(r => r.success).length;
        const failed = results.length - successful;
        
        this.logger.log(`批量开箱完成 - 成功: ${successful}, 失败: ${failed}`, 'SUCCESS');
        
        return results;
    }
}

// 创建开箱器实例
const crateOpener = new ImprovedCrateOpener(gcManager, logger, config);

// 登录
client.logOn({
    accountName: config.steam.accountName,
    password: config.steam.password,
    twoFactorCode: require('steam-totp').getAuthCode(config.steam.sharedSecret)
});

// 导出主要功能
module.exports = {
    client,
    logger,
    gcManager,
    crateOpener,
    unlockCrate: (keyId, caseId, options) => crateOpener.unlockCrate(keyId, caseId, options),
    unlockMultipleCrates: (crateList, options) => crateOpener.unlockMultipleCrates(crateList, options),
    waitForConnection: (timeout) => gcManager.waitForConnection(timeout)
};

// 优雅关闭
process.on('SIGINT', () => {
    logger.log('正在关闭程序...', 'INFO');
    client.logOff();
    process.exit(0);
});
