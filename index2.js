const { EClientPlatformType, LoginSession} = require('steam-session');

const { generateAuthCode } = require('steam-totp');
const EAuthTokenPlatformType = require("steam-session/src/enums-steam/EAuthTokenPlatformType");

// 登录用的账号信息
const account = {
    username: 'ja422373',
    password: 'rAGMZtTKZ3xg',
    shared_secret: 'pbAbhJYebURFyHhqOYWmNIkBVb4='
};

console.log(EClientPlatformType);

(async () => {
    let session = new LoginSession(EAuthTokenPlatformType.WebBrowser);

    session.username = account.username;
    session.password = account.password;
    session.authCode = generateAuthCode(account.shared_secret);

    session.on('authenticated', (result) => {
        console.log('✅ 登录成功');
        console.log('🔑 refreshToken:', result.refreshToken);
        console.log('🪪 accessToken:', result.accessToken);
        console.log('🍪 cookies:', session.cookies);
    });

    session.on('error', (err) => {
        console.error('❌ 登录失败:', err);
    });

    await session.startWithCredentials();
})();