// 修正版 GC 连接 - 使用正确的 sendToGC 参数
const SteamUser = require('steam-user');
const protobuf = require('protobufjs');
const path = require('path');

// 加载配置
let config;
try {
    config = require('./config.js');
} catch (err) {
    console.error('❌ 配置文件不存在');
    process.exit(1);
}

const client = new SteamUser();
const CSGO_APP_ID = 730;

function log(msg, type = 'INFO') {
    const time = new Date().toISOString();
    const emoji = { 
        INFO: '📝', 
        SUCCESS: '✅', 
        ERROR: '❌', 
        WARNING: '⚠️',
        DEBUG: '🔍',
        NETWORK: '🌐'
    }[type] || '📝';
    console.log(`[${time}] ${emoji} ${msg}`);
}

let gcConnected = false;
let messagesReceived = [];

// 加载 proto
let ClientHelloType = null;
try {
    const gcRoot = protobuf.loadSync(path.join(__dirname, "proto", "gc_messages.proto"));
    ClientHelloType = gcRoot.lookupType('CMsgClientHello');
    log('Proto 文件加载成功', 'SUCCESS');
} catch (err) {
    log(`Proto 文件加载失败: ${err.message}`, 'ERROR');
}

// 创建 ClientHello 消息
function createClientHelloMessage() {
    if (!ClientHelloType) {
        log('使用空消息', 'WARNING');
        return Buffer.alloc(0);
    }
    
    const helloData = {
        version: 2000202,
        socache_have_versions: [],
        client_session_need: 1,
        client_launcher: 0,
        steam_launcher: 1
    };
    
    try {
        const encoded = ClientHelloType.encode(helloData).finish();
        log(`ClientHello 创建成功 (${encoded.length} 字节)`, 'SUCCESS');
        return encoded;
    } catch (err) {
        log(`ClientHello 编码失败: ${err.message}`, 'ERROR');
        return Buffer.alloc(0);
    }
}

// 正确的 GC 消息发送方法
function sendGCMessage(msgType, payload, name = '') {
    try {
        log(`发送 ${name || msgType} 消息`, 'NETWORK');
        
        // 关键修正：对于 protobuf 消息，第三个参数应该是 {} 而不是 null
        client.sendToGC(CSGO_APP_ID, msgType, {}, payload);
        
        log(`${name || msgType} 消息已发送`, 'SUCCESS');
        return true;
    } catch (err) {
        log(`发送 ${name || msgType} 失败: ${err.message}`, 'ERROR');
        return false;
    }
}

// 尝试连接 GC
function attemptGCConnection() {
    log('开始 GC 连接尝试', 'INFO');
    
    // 1. 发送 ClientHello
    const helloMessage = createClientHelloMessage();
    sendGCMessage(4006, helloMessage, 'ClientHello');
    
    // 2. 等待几秒后尝试其他消息
    setTimeout(() => {
        if (!gcConnected) {
            log('尝试发送 ClientWelcome 请求', 'INFO');
            sendGCMessage(4004, Buffer.alloc(0), 'ClientWelcome');
        }
    }, 3000);
    
    // 3. 再等待几秒后发送连接状态
    setTimeout(() => {
        if (!gcConnected) {
            log('尝试发送连接状态消息', 'INFO');
            sendGCMessage(4001, Buffer.alloc(0), 'ConnectionStatus');
        }
    }, 6000);
    
    // 4. 最后重新发送 ClientHello
    setTimeout(() => {
        if (!gcConnected) {
            log('重新发送 ClientHello', 'INFO');
            const helloMessage2 = createClientHelloMessage();
            sendGCMessage(4006, helloMessage2, 'ClientHello (重试)');
        }
    }, 10000);
}

// Steam 事件处理
client.on('loggedOn', () => {
    log('Steam 登录成功', 'SUCCESS');
    log(`Steam ID: ${client.steamID}`, 'INFO');
    client.gamesPlayed([CSGO_APP_ID]);
});

client.on('appLaunched', (appid) => {
    if (appid === CSGO_APP_ID) {
        log('CSGO 启动成功', 'SUCCESS');
        
        // 等待游戏完全加载后尝试连接
        setTimeout(() => {
            attemptGCConnection();
        }, 3000);
    }
});

client.on('gcConnected', (appid) => {
    if (appid === CSGO_APP_ID) {
        gcConnected = true;
        log('🎉 GC 连接成功！', 'SUCCESS');
        
        setTimeout(() => {
            log('=== 连接成功统计 ===', 'SUCCESS');
            log(`收到消息数: ${messagesReceived.length}`, 'INFO');
            if (messagesReceived.length > 0) {
                log(`消息类型: ${messagesReceived.join(', ')}`, 'INFO');
            }
            log('现在可以进行开箱操作', 'SUCCESS');
        }, 2000);
    }
});

client.on('gcDisconnected', (appid) => {
    if (appid === CSGO_APP_ID) {
        gcConnected = false;
        log('GC 连接断开', 'WARNING');
        
        // 尝试重连
        setTimeout(() => {
            if (!gcConnected) {
                log('尝试重新连接', 'INFO');
                attemptGCConnection();
            }
        }, 5000);
    }
});

// 关键：使用正确的事件名称
client.on('receivedFromGC', (appid, msgType, payload) => {
    if (appid === CSGO_APP_ID) {
        messagesReceived.push(msgType);
        log(`收到 GC 消息: ${msgType} (${payload.length} 字节)`, 'SUCCESS');
        
        // 处理特定消息
        const messageNames = {
            4004: 'ClientWelcome',
            4005: 'ServerWelcome',
            4007: 'ServerHello',
            1090: 'ItemDrop',
            1008: 'UnlockCrateResponse'
        };
        
        if (messageNames[msgType]) {
            log(`  -> ${messageNames[msgType]}`, 'SUCCESS');
        }
        
        // 如果收到任何消息，说明连接是工作的
        if (!gcConnected) {
            log('收到 GC 消息，连接可能已建立', 'SUCCESS');
        }
    }
});

client.on('error', (err) => {
    log(`Steam 错误: ${err.message}`, 'ERROR');
});

// 测试开箱函数
async function testOpenCrate(keyId, caseId) {
    if (!gcConnected && messagesReceived.length === 0) {
        log('GC 未连接且未收到消息，无法开箱', 'ERROR');
        return false;
    }
    
    try {
        log(`测试开箱 - 钥匙: ${keyId}, 箱子: ${caseId}`, 'INFO');
        
        // 加载开箱 proto
        const crateRoot = protobuf.loadSync(path.join(__dirname, "proto", "opencrate.proto"));
        const OpenCrateType = crateRoot.lookupType('CMsgOpenCrate');
        
        const openCrateData = {
            tool_item_id: keyId,
            subject_item_id: caseId,
            for_rental: false
        };
        
        const encoded = OpenCrateType.encode(openCrateData).finish();
        
        // 使用正确的参数发送开箱请求
        const sent = sendGCMessage(2534, encoded, '开箱请求');
        
        if (sent) {
            log('开箱请求已发送，等待响应...', 'SUCCESS');
            return true;
        } else {
            log('开箱请求发送失败', 'ERROR');
            return false;
        }
        
    } catch (err) {
        log(`开箱失败: ${err.message}`, 'ERROR');
        return false;
    }
}

// 主函数
async function main() {
    log('=== 修正版 GC 连接测试 ===', 'INFO');
    log('使用正确的 sendToGC 参数格式', 'INFO');
    
    client.logOn({
        accountName: config.steam.accountName,
        password: config.steam.password,
        twoFactorCode: require('steam-totp').getAuthCode(config.steam.sharedSecret)
    });
    
    // 设置超时检查
    setTimeout(() => {
        if (!gcConnected && messagesReceived.length === 0) {
            log('连接超时，未收到任何 GC 消息', 'ERROR');
            log('=== 诊断信息 ===', 'WARNING');
            log('1. 检查 CSGO 服务器状态', 'INFO');
            log('2. 检查网络连接', 'INFO');
            log('3. 尝试重启 Steam', 'INFO');
            process.exit(1);
        } else if (messagesReceived.length > 0) {
            log('收到了 GC 消息，连接可能正常工作', 'SUCCESS');
            
            // 如果收到消息但没有 gcConnected 事件，可能仍然可以开箱
            log('尝试测试开箱功能...', 'INFO');
            // testOpenCrate(***********, ***********);
        }
    }, 30000);
}

// 优雅退出
process.on('SIGINT', () => {
    log('程序被中断', 'WARNING');
    log(`总共收到 ${messagesReceived.length} 个 GC 消息`, 'INFO');
    client.logOff();
    process.exit(0);
});

// 导出函数
module.exports = {
    client,
    gcConnected: () => gcConnected,
    messagesReceived: () => messagesReceived,
    testOpenCrate,
    log
};

if (require.main === module) {
    main().catch(err => {
        console.error('程序错误:', err);
        process.exit(1);
    });
}
